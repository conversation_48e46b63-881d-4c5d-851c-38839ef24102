import { json } from '@sveltejs/kit';
import { db } from '$lib/server/db';
import { users, news, gallery, comments, messages, scheduledContent, contentAuthorship, auditLogs } from '$lib/server/db/schema';
import { eq } from 'drizzle-orm';
import type { RequestHandler } from './$types';
import logger from '$lib/server/services/logger';

// POST /api/admin/post-as-user - Post content on behalf of a user
export const POST: RequestHandler = async ({ request, locals, getClientAddress }) => {
  try {
    // Check if user is authenticated and has admin privileges
    if (!locals.user || locals.user.role !== 'admin') {
      return json({
        success: false,
        error: 'Unauthorized - Admin access required'
      }, { status: 403 });
    }

    const body = await request.json();
    const { contentType, asUserId, contentData, scheduledFor } = body;

    // Validate required fields
    if (!contentType || !asUserId || !contentData) {
      return json({
        success: false,
        error: 'Content type, user ID, and content data are required'
      }, { status: 400 });
    }

    // Validate content type
    if (!['news', 'gallery', 'comment', 'message'].includes(contentType)) {
      return json({
        success: false,
        error: 'Invalid content type'
      }, { status: 400 });
    }

    // Check if target user exists
    const targetUser = await db.select()
      .from(users)
      .where(eq(users.id, asUserId))
      .limit(1);

    if (targetUser.length === 0) {
      return json({
        success: false,
        error: 'Target user not found'
      }, { status: 404 });
    }

    let contentId: number;
    let result: any;

    // If scheduled, create scheduled content entry
    if (scheduledFor && !contentData.published) {
      const scheduledResult = await db.insert(scheduledContent).values({
        contentType,
        contentData: JSON.stringify(contentData),
        asUserId,
        scheduledFor,
        createdByUserId: locals.user.id
      }).returning();

      // Log admin action
      await logAdminAction(
        locals.user.id,
        'schedule_content',
        'scheduled_content',
        scheduledResult[0].id,
        asUserId,
        {
          contentType,
          scheduledFor,
          contentPreview: contentData.title || contentData.content?.substring(0, 100)
        },
        getClientAddress()
      );

      return json({
        success: true,
        data: scheduledResult[0],
        message: `Content scheduled for ${new Date(scheduledFor).toLocaleString()}`
      });
    }

    // Post content immediately
    switch (contentType) {
      case 'news':
        result = await db.insert(news).values({
          title: contentData.title,
          content: contentData.content,
          imageUrl: contentData.imageUrl || null,
          authorId: asUserId,
          published: contentData.published || true
        }).returning();
        contentId = result[0].id;
        break;

      case 'gallery':
        result = await db.insert(gallery).values({
          title: contentData.title,
          description: contentData.description || null,
          imageUrl: contentData.imageUrl,
          thumbnailUrl: contentData.thumbnailUrl || contentData.imageUrl,
          authorId: asUserId,
          published: contentData.published || true
        }).returning();
        contentId = result[0].id;
        break;

      case 'comment':
        result = await db.insert(comments).values({
          userId: asUserId,
          content: contentData.content,
          itemType: contentData.itemType as 'news' | 'gallery',
          itemId: contentData.itemId,
          approved: true // Auto-approve admin-created comments
        }).returning();
        contentId = result[0].id;
        break;

      case 'message':
        result = await db.insert(messages).values({
          userId: asUserId,
          content: contentData.content,
          approved: true // Auto-approve admin-created messages
        }).returning();
        contentId = result[0].id;
        break;

      default:
        return json({
          success: false,
          error: 'Unsupported content type'
        }, { status: 400 });
    }

    // Record content authorship
    await db.insert(contentAuthorship).values({
      contentType,
      contentId,
      actualAuthorId: locals.user.id,
      displayAuthorId: asUserId,
      isSimulated: true
    });

    // Log admin action
    await logAdminAction(
      locals.user.id,
      'post_as_user',
      contentType,
      contentId,
      asUserId,
      {
        contentType,
        contentPreview: contentData.title || contentData.content?.substring(0, 100),
        published: contentData.published
      },
      getClientAddress()
    );

    // Update user's last active time
    await db.update(users)
      .set({ lastActiveAt: new Date().toISOString() })
      .where(eq(users.id, asUserId));

    logger.info('Content posted as user', {
      adminUser: locals.user.username,
      targetUser: targetUser[0].username,
      contentType,
      contentId,
      published: contentData.published
    });

    return json({
      success: true,
      data: result[0],
      message: `${contentType.charAt(0).toUpperCase() + contentType.slice(1)} posted successfully as ${targetUser[0].displayName}`
    });

  } catch (error) {
    logger.error('Error posting content as user:', error);
    return json({
      success: false,
      error: 'Failed to post content'
    }, { status: 500 });
  }
};

// Helper function to log admin actions
async function logAdminAction(
  adminUserId: number,
  action: string,
  targetType: string,
  targetId: number | null,
  targetUserId: number | null,
  details: any,
  ipAddress: string
) {
  try {
    await db.insert(auditLogs).values({
      adminUserId,
      action,
      targetType,
      targetId,
      targetUserId,
      details: JSON.stringify(details),
      ipAddress
    });
  } catch (error) {
    logger.error('Failed to log admin action:', error);
  }
}
