<script lang="ts">
	import { onMount } from 'svelte';
	import { goto } from '$app/navigation';
	import LoadingSpinner from '$lib/components/LoadingSpinner.svelte';
	import ErrorMessage from '$lib/components/ErrorMessage.svelte';
	import type { PageData } from './$types';

	export let data: PageData;

	// State
	let auditLogs: any[] = [];
	let isLoading = false;
	let error = '';
	let selectedLogId: number | null = null;
	let showLogModal = false;

	// Filters
	let actionFilter = '';
	let adminUserFilter = '';
	let targetTypeFilter = '';
	let dateFromFilter = '';
	let dateToFilter = '';

	// Pagination
	let currentPage = 1;
	let logsPerPage = 50;
	let totalLogs = 0;
	let totalPages = 0;

	// Filter options
	const actionOptions = [
		{ value: '', label: 'All Actions' },
		{ value: 'create', label: 'Create' },
		{ value: 'update', label: 'Update' },
		{ value: 'delete', label: 'Delete' },
		{ value: 'login', label: 'Login' },
		{ value: 'logout', label: 'Logout' },
		{ value: 'post_as_user', label: 'Post as User' },
		{ value: 'schedule_content', label: 'Schedule Content' },
		{ value: 'publish_scheduled_content', label: 'Publish Scheduled' },
		{ value: 'cancel_scheduled_content', label: 'Cancel Scheduled' },
		{ value: 'bulk_operation', label: 'Bulk Operation' }
	];

	const targetTypeOptions = [
		{ value: '', label: 'All Types' },
		{ value: 'user', label: 'User' },
		{ value: 'news', label: 'News' },
		{ value: 'gallery', label: 'Gallery' },
		{ value: 'comment', label: 'Comment' },
		{ value: 'message', label: 'Message' },
		{ value: 'scheduled_content', label: 'Scheduled Content' },
		{ value: 'media', label: 'Media' },
		{ value: 'site_settings', label: 'Site Settings' }
	];

	// Load audit logs
	async function loadAuditLogs() {
		isLoading = true;
		error = '';

		try {
			const params = new URLSearchParams({
				page: currentPage.toString(),
				limit: logsPerPage.toString(),
				action: actionFilter,
				adminUser: adminUserFilter,
				targetType: targetTypeFilter,
				...(dateFromFilter && { dateFrom: dateFromFilter }),
				...(dateToFilter && { dateTo: dateToFilter })
			});

			const response = await fetch(`/api/admin/audit-logs?${params}`);
			const result = await response.json();

			if (response.ok && result.success) {
				auditLogs = result.data.logs;
				totalLogs = result.data.total;
				totalPages = Math.ceil(totalLogs / logsPerPage);
			} else {
				error = result.error || 'Failed to load audit logs';
			}
		} catch (err) {
			console.error('Error loading audit logs:', err);
			error = 'Failed to load audit logs';
		} finally {
			isLoading = false;
		}
	}

	// Handle filter changes
	function handleFilterChange() {
		currentPage = 1;
		loadAuditLogs();
	}

	// Handle page change
	function changePage(page: number) {
		if (page >= 1 && page <= totalPages) {
			currentPage = page;
			loadAuditLogs();
		}
	}

	// View log details
	function viewLogDetails(logId: number) {
		selectedLogId = logId;
		showLogModal = true;
	}

	// Get selected log
	function getSelectedLog() {
		return auditLogs.find(log => log.id === selectedLogId);
	}

	// Format date for display
	function formatDate(dateString: string): string {
		return new Date(dateString).toLocaleString('en-US', {
			year: 'numeric',
			month: 'short',
			day: 'numeric',
			hour: '2-digit',
			minute: '2-digit',
			second: '2-digit'
		});
	}

	// Get action badge class
	function getActionBadgeClass(action: string): string {
		if (action.includes('create')) return 'action-create';
		if (action.includes('update')) return 'action-update';
		if (action.includes('delete')) return 'action-delete';
		if (action.includes('login')) return 'action-login';
		if (action.includes('post_as_user')) return 'action-post';
		if (action.includes('schedule')) return 'action-schedule';
		if (action.includes('bulk')) return 'action-bulk';
		return 'action-other';
	}

	// Get risk level based on action
	function getRiskLevel(action: string): string {
		if (action.includes('delete') || action.includes('bulk')) return 'high';
		if (action.includes('create') || action.includes('update') || action.includes('post_as_user')) return 'medium';
		return 'low';
	}

	// Export audit logs
	async function exportLogs(format: 'csv' | 'json') {
		try {
			const params = new URLSearchParams({
				format,
				action: actionFilter,
				adminUser: adminUserFilter,
				targetType: targetTypeFilter,
				...(dateFromFilter && { dateFrom: dateFromFilter }),
				...(dateToFilter && { dateTo: dateToFilter })
			});

			const response = await fetch(`/api/admin/audit-logs/export?${params}`);
			
			if (response.ok) {
				const blob = await response.blob();
				const url = window.URL.createObjectURL(blob);
				const a = document.createElement('a');
				a.href = url;
				a.download = `audit-logs-${new Date().toISOString().split('T')[0]}.${format}`;
				document.body.appendChild(a);
				a.click();
				window.URL.revokeObjectURL(url);
				document.body.removeChild(a);
			} else {
				const result = await response.json();
				error = result.error || 'Failed to export logs';
			}
		} catch (err) {
			console.error('Error exporting logs:', err);
			error = 'Failed to export logs';
		}
	}

	// Initialize
	onMount(() => {
		loadAuditLogs();
	});

	// Clear error after 8 seconds
	$: if (error) {
		setTimeout(() => {
			error = '';
		}, 8000);
	}
</script>

<svelte:head>
	<title>Audit Logs - FWFC Admin</title>
	<meta name="description" content="View and monitor admin activity logs" />
</svelte:head>

<div class="audit-logs">
	<header class="page-header">
		<div class="header-content">
			<h1>Audit Logs</h1>
			<p class="header-subtitle">
				Monitor and track all administrative actions for security and accountability
			</p>
		</div>
		<div class="header-actions">
			<button
				class="btn secondary"
				onclick={() => exportLogs('csv')}
				disabled={isLoading}
			>
				📊 Export CSV
			</button>
			<button
				class="btn secondary"
				onclick={() => exportLogs('json')}
				disabled={isLoading}
			>
				📄 Export JSON
			</button>
			<button
				class="btn secondary"
				onclick={() => goto('/admin')}
				disabled={isLoading}
			>
				← Back to Admin
			</button>
		</div>
	</header>

	<!-- Error Messages -->
	{#if error}
		<ErrorMessage
			title="Error"
			message={error}
			type="error"
			dismissible={true}
			onDismiss={() => error = ''}
		/>
	{/if}

	<!-- Filters -->
	<div class="filters-section">
		<div class="filters-grid">
			<div class="filter-group">
				<label for="action-filter">Action:</label>
				<select
					id="action-filter"
					bind:value={actionFilter}
					onchange={handleFilterChange}
					disabled={isLoading}
				>
					{#each actionOptions as option}
						<option value={option.value}>{option.label}</option>
					{/each}
				</select>
			</div>

			<div class="filter-group">
				<label for="target-type-filter">Target Type:</label>
				<select
					id="target-type-filter"
					bind:value={targetTypeFilter}
					onchange={handleFilterChange}
					disabled={isLoading}
				>
					{#each targetTypeOptions as option}
						<option value={option.value}>{option.label}</option>
					{/each}
				</select>
			</div>

			<div class="filter-group">
				<label for="admin-user-filter">Admin User:</label>
				<input
					type="text"
					id="admin-user-filter"
					bind:value={adminUserFilter}
					onchange={handleFilterChange}
					placeholder="Search by username..."
					disabled={isLoading}
				/>
			</div>

			<div class="filter-group">
				<label for="date-from-filter">From Date:</label>
				<input
					type="datetime-local"
					id="date-from-filter"
					bind:value={dateFromFilter}
					onchange={handleFilterChange}
					disabled={isLoading}
				/>
			</div>

			<div class="filter-group">
				<label for="date-to-filter">To Date:</label>
				<input
					type="datetime-local"
					id="date-to-filter"
					bind:value={dateToFilter}
					onchange={handleFilterChange}
					disabled={isLoading}
				/>
			</div>
		</div>
	</div>

	<!-- Summary Stats -->
	<div class="stats-section">
		<div class="stat-card">
			<h3>Total Logs</h3>
			<p class="stat-number">{totalLogs.toLocaleString()}</p>
		</div>
		<div class="stat-card">
			<h3>Current Page</h3>
			<p class="stat-number">{currentPage} of {totalPages}</p>
		</div>
		<div class="stat-card">
			<h3>Showing</h3>
			<p class="stat-number">{auditLogs.length} logs</p>
		</div>
	</div>

	<!-- Logs Table -->
	<div class="table-container">
		{#if isLoading}
			<div class="loading-container">
				<LoadingSpinner />
				<p>Loading audit logs...</p>
			</div>
		{:else if auditLogs.length === 0}
			<div class="empty-state">
				<h3>No audit logs found</h3>
				<p>There are no audit logs matching your current filters.</p>
			</div>
		{:else}
			<table class="logs-table">
				<thead>
					<tr>
						<th scope="col">Timestamp</th>
						<th scope="col">Admin User</th>
						<th scope="col">Action</th>
						<th scope="col">Target</th>
						<th scope="col">Risk Level</th>
						<th scope="col">IP Address</th>
						<th scope="col">Details</th>
					</tr>
				</thead>
				<tbody>
					{#each auditLogs as log}
						<tr class="log-row risk-{getRiskLevel(log.action)}">
							<td class="timestamp-cell">
								{formatDate(log.createdAt)}
							</td>
							<td class="user-cell">
								<div class="user-info">
									<strong>{log.adminUser?.displayName || 'Unknown'}</strong>
									<small>@{log.adminUser?.username || 'unknown'}</small>
								</div>
							</td>
							<td>
								<span class="action-badge {getActionBadgeClass(log.action)}">
									{log.action.replace(/_/g, ' ')}
								</span>
							</td>
							<td class="target-cell">
								<div class="target-info">
									<span class="target-type">{log.targetType}</span>
									{#if log.targetId}
										<small>ID: {log.targetId}</small>
									{/if}
									{#if log.targetUser}
										<small>User: {log.targetUser.displayName}</small>
									{/if}
								</div>
							</td>
							<td>
								<span class="risk-badge risk-{getRiskLevel(log.action)}">
									{getRiskLevel(log.action)}
								</span>
							</td>
							<td class="ip-cell">
								{log.ipAddress || 'N/A'}
							</td>
							<td class="actions-cell">
								<button
									class="btn-small"
									onclick={() => viewLogDetails(log.id)}
									title="View details"
								>
									👁️
								</button>
							</td>
						</tr>
					{/each}
				</tbody>
			</table>

			<!-- Pagination -->
			{#if totalPages > 1}
				<div class="pagination">
					<button
						class="btn secondary"
						onclick={() => changePage(currentPage - 1)}
						disabled={currentPage === 1 || isLoading}
					>
						← Previous
					</button>

					<span class="page-info">
						Page {currentPage} of {totalPages} ({totalLogs} total logs)
					</span>

					<button
						class="btn secondary"
						onclick={() => changePage(currentPage + 1)}
						disabled={currentPage === totalPages || isLoading}
					>
						Next →
					</button>
				</div>
			{/if}
		{/if}
	</div>
</div>

<!-- Log Details Modal -->
{#if showLogModal && selectedLogId}
	{@const selectedLog = getSelectedLog()}
	{#if selectedLog}
		<div
			class="modal-overlay"
			role="dialog"
			aria-modal="true"
			tabindex="-1"
			onclick={() => { showLogModal = false; selectedLogId = null; }}
			onkeydown={(e) => { if (e.key === 'Escape') { showLogModal = false; selectedLogId = null; } }}
		>
			<div
				class="modal"
				role="document"
				onclick={(e) => e.stopPropagation()}
				onkeydown={(e) => e.stopPropagation()}
			>
				<div class="modal-header">
					<h2>Audit Log Details</h2>
					<button
						class="close-button"
						onclick={() => { showLogModal = false; selectedLogId = null; }}
						aria-label="Close log details"
					>
						×
					</button>
				</div>

				<div class="modal-body">
					<div class="log-details">
						<div class="detail-section">
							<h3>Basic Information</h3>
							<div class="detail-grid">
								<div class="detail-item">
									<span class="detail-label">Timestamp:</span>
									<span>{formatDate(selectedLog.createdAt)}</span>
								</div>
								<div class="detail-item">
									<span class="detail-label">Admin User:</span>
									<span>{selectedLog.adminUser?.displayName || 'Unknown'} (@{selectedLog.adminUser?.username || 'unknown'})</span>
								</div>
								<div class="detail-item">
									<span class="detail-label">Action:</span>
									<span class="action-badge {getActionBadgeClass(selectedLog.action)}">
										{selectedLog.action.replace(/_/g, ' ')}
									</span>
								</div>
								<div class="detail-item">
									<span class="detail-label">Risk Level:</span>
									<span class="risk-badge risk-{getRiskLevel(selectedLog.action)}">
										{getRiskLevel(selectedLog.action)}
									</span>
								</div>
							</div>
						</div>

						<div class="detail-section">
							<h3>Target Information</h3>
							<div class="detail-grid">
								<div class="detail-item">
									<span class="detail-label">Target Type:</span>
									<span>{selectedLog.targetType}</span>
								</div>
								{#if selectedLog.targetId}
									<div class="detail-item">
										<span class="detail-label">Target ID:</span>
										<span>{selectedLog.targetId}</span>
									</div>
								{/if}
								{#if selectedLog.targetUser}
									<div class="detail-item">
										<span class="detail-label">Target User:</span>
										<span>{selectedLog.targetUser.displayName} (@{selectedLog.targetUser.username})</span>
									</div>
								{/if}
							</div>
						</div>

						<div class="detail-section">
							<h3>Technical Information</h3>
							<div class="detail-grid">
								<div class="detail-item">
									<span class="detail-label">IP Address:</span>
									<span>{selectedLog.ipAddress || 'N/A'}</span>
								</div>
								<div class="detail-item">
									<span class="detail-label">User Agent:</span>
									<span class="user-agent">{selectedLog.userAgent || 'N/A'}</span>
								</div>
							</div>
						</div>

						{#if selectedLog.details}
							<div class="detail-section">
								<h3>Additional Details</h3>
								<div class="details-json">
									<pre>{JSON.stringify(JSON.parse(selectedLog.details), null, 2)}</pre>
								</div>
							</div>
						{/if}
					</div>
				</div>

				<div class="modal-footer">
					<button
						class="btn secondary"
						onclick={() => { showLogModal = false; selectedLogId = null; }}
					>
						Close
					</button>
				</div>
			</div>
		</div>
	{/if}
{/if}

<style>
	.audit-logs {
		padding: 2rem;
		max-width: 1400px;
		margin: 0 auto;
		color: var(--theme-text-primary);
	}

	/* Page Header */
	.page-header {
		display: flex;
		justify-content: space-between;
		align-items: flex-start;
		margin-bottom: 2rem;
		padding-bottom: 1rem;
		border-bottom: 2px solid var(--theme-border);
	}

	.header-content h1 {
		margin: 0 0 0.5rem 0;
		font-size: 2.5rem;
		font-weight: 700;
		color: var(--theme-text-primary);
	}

	.header-subtitle {
		margin: 0;
		font-size: 1.1rem;
		color: var(--theme-text-secondary);
		line-height: 1.5;
	}

	.header-actions {
		display: flex;
		gap: 1rem;
		flex-wrap: wrap;
	}

	/* Filters */
	.filters-section {
		margin-bottom: 2rem;
		padding: 1.5rem;
		border: 1px solid var(--theme-border);
		border-radius: 8px;
		background-color: var(--theme-card-bg);
	}

	.filters-grid {
		display: grid;
		grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
		gap: 1rem;
	}

	.filter-group {
		display: flex;
		flex-direction: column;
		gap: 0.5rem;
	}

	.filter-group label {
		font-weight: 600;
		color: var(--theme-text-primary);
		font-size: 0.9rem;
	}

	.filter-group select,
	.filter-group input {
		padding: 0.75rem;
		border: 2px solid var(--theme-input-border);
		border-radius: 6px;
		background-color: var(--theme-input-bg);
		color: var(--theme-text-primary);
		font-size: 1rem;
	}

	/* Stats Section */
	.stats-section {
		display: grid;
		grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
		gap: 1rem;
		margin-bottom: 2rem;
	}

	.stat-card {
		padding: 1.5rem;
		border: 1px solid var(--theme-border);
		border-radius: 8px;
		background-color: var(--theme-card-bg);
		text-align: center;
	}

	.stat-card h3 {
		margin: 0 0 0.5rem 0;
		font-size: 0.9rem;
		color: var(--theme-text-secondary);
		text-transform: uppercase;
		letter-spacing: 0.5px;
	}

	.stat-number {
		margin: 0;
		font-size: 2rem;
		font-weight: 700;
		color: var(--theme-accent-primary);
	}

	/* Buttons */
	.btn {
		display: inline-flex;
		align-items: center;
		justify-content: center;
		gap: 0.5rem;
		padding: 0.75rem 1.5rem;
		border: 2px solid var(--theme-border);
		border-radius: 6px;
		font-size: 0.9rem;
		font-weight: 600;
		cursor: pointer;
		transition: all 0.2s ease;
		text-decoration: none;
		background-color: var(--theme-button-bg);
		color: var(--theme-button-text);
	}

	.btn:disabled {
		opacity: 0.6;
		cursor: not-allowed;
		transform: none !important;
	}

	.btn.secondary {
		background-color: var(--theme-button-bg);
		color: var(--theme-button-text);
		border-color: var(--theme-border);
	}

	.btn.secondary:hover:not(:disabled) {
		background-color: var(--theme-bg-tertiary);
		border-color: var(--theme-accent-primary);
		transform: translateY(-1px);
	}

	/* Table Styles */
	.table-container {
		background-color: var(--theme-card-bg);
		border: 1px solid var(--theme-border);
		border-radius: 8px;
		overflow: hidden;
	}

	.loading-container {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 4rem 2rem;
		gap: 1rem;
	}

	.loading-container p {
		color: var(--theme-text-secondary);
		font-size: 1.1rem;
	}

	.empty-state {
		text-align: center;
		padding: 4rem 2rem;
	}

	.empty-state h3 {
		margin: 0 0 1rem 0;
		color: var(--theme-text-primary);
		font-size: 1.5rem;
	}

	.empty-state p {
		margin: 0 0 2rem 0;
		color: var(--theme-text-secondary);
		font-size: 1.1rem;
	}

	.logs-table {
		width: 100%;
		border-collapse: collapse;
		font-size: 0.9rem;
	}

	.logs-table th,
	.logs-table td {
		padding: 1rem;
		text-align: left;
		border-bottom: 1px solid var(--theme-border);
	}

	.logs-table th {
		background-color: var(--theme-bg-secondary);
		font-weight: 600;
		color: var(--theme-text-primary);
		position: sticky;
		top: 0;
		z-index: 1;
	}

	.logs-table tr:hover {
		background-color: var(--theme-bg-tertiary);
	}

	.log-row.risk-high {
		border-left: 4px solid var(--theme-accent-danger, #dc3545);
	}

	.log-row.risk-medium {
		border-left: 4px solid var(--theme-accent-warning, #ffc107);
	}

	.log-row.risk-low {
		border-left: 4px solid var(--theme-accent-success, #28a745);
	}

	.timestamp-cell {
		font-size: 0.85rem;
		color: var(--theme-text-secondary);
		white-space: nowrap;
		min-width: 150px;
	}

	.user-cell .user-info strong {
		display: block;
		color: var(--theme-text-primary);
	}

	.user-cell .user-info small {
		color: var(--theme-text-secondary);
		font-size: 0.8rem;
	}

	.action-badge {
		display: inline-block;
		padding: 0.25rem 0.75rem;
		border-radius: 12px;
		font-size: 0.8rem;
		font-weight: 600;
		text-transform: uppercase;
		letter-spacing: 0.5px;
	}

	.action-create {
		background-color: var(--theme-accent-success, #28a745);
		color: white;
	}

	.action-update {
		background-color: var(--theme-accent-info, #17a2b8);
		color: white;
	}

	.action-delete {
		background-color: var(--theme-accent-danger, #dc3545);
		color: white;
	}

	.action-login {
		background-color: var(--theme-accent-primary, #4caf50);
		color: white;
	}

	.action-post {
		background-color: var(--theme-accent-warning, #ffc107);
		color: #212529;
	}

	.action-schedule {
		background-color: var(--theme-accent-secondary, #6c757d);
		color: white;
	}

	.action-bulk {
		background-color: var(--theme-accent-danger, #dc3545);
		color: white;
	}

	.action-other {
		background-color: var(--theme-accent-secondary, #6c757d);
		color: white;
	}

	.target-cell .target-info .target-type {
		display: block;
		font-weight: 600;
		color: var(--theme-text-primary);
		text-transform: capitalize;
	}

	.target-cell .target-info small {
		display: block;
		color: var(--theme-text-secondary);
		font-size: 0.8rem;
	}

	.risk-badge {
		display: inline-block;
		padding: 0.25rem 0.75rem;
		border-radius: 12px;
		font-size: 0.8rem;
		font-weight: 600;
		text-transform: uppercase;
		letter-spacing: 0.5px;
	}

	.risk-high {
		background-color: var(--theme-accent-danger, #dc3545);
		color: white;
	}

	.risk-medium {
		background-color: var(--theme-accent-warning, #ffc107);
		color: #212529;
	}

	.risk-low {
		background-color: var(--theme-accent-success, #28a745);
		color: white;
	}

	.ip-cell {
		font-family: monospace;
		font-size: 0.85rem;
		color: var(--theme-text-secondary);
	}

	.actions-cell {
		width: 80px;
		text-align: center;
	}

	.btn-small {
		display: inline-flex;
		align-items: center;
		justify-content: center;
		width: 32px;
		height: 32px;
		border: 1px solid var(--theme-border);
		border-radius: 4px;
		font-size: 0.8rem;
		cursor: pointer;
		transition: all 0.2s ease;
		background-color: var(--theme-button-bg);
		color: var(--theme-button-text);
	}

	.btn-small:hover {
		transform: scale(1.1);
		background-color: var(--theme-accent-primary);
		color: white;
	}

	/* Pagination */
	.pagination {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 1rem;
		border-top: 1px solid var(--theme-border);
		background-color: var(--theme-bg-secondary);
	}

	.page-info {
		color: var(--theme-text-secondary);
		font-size: 0.9rem;
	}

	/* Modal Styles */
	.modal-overlay {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: rgba(0, 0, 0, 0.5);
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 1000;
		padding: 2rem;
	}

	.modal {
		background-color: var(--theme-card-bg);
		border-radius: 8px;
		max-width: 800px;
		width: 100%;
		max-height: 90vh;
		overflow: hidden;
		display: flex;
		flex-direction: column;
		box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
	}

	.modal-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 1.5rem;
		border-bottom: 1px solid var(--theme-border);
		background-color: var(--theme-bg-secondary);
	}

	.modal-header h2 {
		margin: 0;
		color: var(--theme-text-primary);
		font-size: 1.5rem;
	}

	.close-button {
		background: none;
		border: none;
		font-size: 1.5rem;
		cursor: pointer;
		color: var(--theme-text-secondary);
		padding: 0.5rem;
		border-radius: 4px;
		transition: all 0.2s ease;
	}

	.close-button:hover {
		background-color: var(--theme-bg-tertiary);
		color: var(--theme-text-primary);
	}

	.modal-body {
		padding: 1.5rem;
		overflow-y: auto;
		flex: 1;
	}

	.modal-footer {
		padding: 1rem 1.5rem;
		border-top: 1px solid var(--theme-border);
		background-color: var(--theme-bg-secondary);
		display: flex;
		justify-content: flex-end;
		gap: 1rem;
	}

	/* Log Details */
	.log-details {
		display: grid;
		gap: 2rem;
	}

	.detail-section {
		border: 1px solid var(--theme-border);
		border-radius: 6px;
		padding: 1.5rem;
		background-color: var(--theme-bg-secondary);
	}

	.detail-section h3 {
		margin: 0 0 1rem 0;
		color: var(--theme-text-primary);
		font-size: 1.1rem;
		border-bottom: 1px solid var(--theme-border);
		padding-bottom: 0.5rem;
	}

	.detail-grid {
		display: grid;
		grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
		gap: 1rem;
	}

	.detail-item {
		display: flex;
		flex-direction: column;
		gap: 0.25rem;
	}

	.detail-label {
		font-weight: 600;
		color: var(--theme-text-secondary);
		font-size: 0.9rem;
		text-transform: uppercase;
		letter-spacing: 0.5px;
	}

	.detail-item > span:not(.detail-label) {
		color: var(--theme-text-primary);
		font-size: 1rem;
	}

	.user-agent {
		font-family: monospace;
		font-size: 0.85rem;
		word-break: break-all;
		color: var(--theme-text-secondary);
	}

	.details-json {
		background-color: var(--theme-bg-tertiary);
		border: 1px solid var(--theme-border);
		border-radius: 4px;
		padding: 1rem;
		overflow-x: auto;
	}

	.details-json pre {
		margin: 0;
		font-family: 'Courier New', monospace;
		font-size: 0.85rem;
		color: var(--theme-text-primary);
		white-space: pre-wrap;
		word-wrap: break-word;
	}

	/* Responsive Design */
	@media (max-width: 768px) {
		.audit-logs {
			padding: 1rem;
		}

		.page-header {
			flex-direction: column;
			align-items: stretch;
			gap: 1rem;
		}

		.header-actions {
			justify-content: flex-start;
		}

		.filters-grid {
			grid-template-columns: 1fr;
		}

		.stats-section {
			grid-template-columns: 1fr;
		}

		.logs-table {
			font-size: 0.8rem;
		}

		.logs-table th,
		.logs-table td {
			padding: 0.5rem;
		}

		.timestamp-cell {
			min-width: 120px;
		}

		.modal {
			margin: 1rem;
			max-height: 95vh;
		}

		.modal-header {
			padding: 1rem;
		}

		.modal-body {
			padding: 1rem;
		}

		.modal-footer {
			padding: 1rem;
		}

		.detail-grid {
			grid-template-columns: 1fr;
		}

		.detail-section {
			padding: 1rem;
		}
	}
</style>
