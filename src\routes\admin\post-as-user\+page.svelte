<script lang="ts">
	import { onMount } from 'svelte';
	import { goto } from '$app/navigation';
	import LoadingSpinner from '$lib/components/LoadingSpinner.svelte';
	import ErrorMessage from '$lib/components/ErrorMessage.svelte';
	import ImageSelector from '$lib/components/admin/ImageSelector.svelte';
	import RichTextEditor from '$lib/components/admin/RichTextEditor.svelte';
	import type { PageData } from './$types';

	export let data: PageData;

	// Content types
	const contentTypes = [
		{ value: 'news', label: 'News Article' },
		{ value: 'gallery', label: 'Gallery Item' },
		{ value: 'comment', label: 'Comment' },
		{ value: 'message', label: 'Message' }
	];

	// Form state
	let selectedContentType = 'news';
	let selectedUserId: number | null = null;
	let users: any[] = [];
	let isLoading = false;
	let error = '';
	let successMessage = '';
	let showPreview = false;

	// Content data
	let contentData = {
		title: '',
		content: '',
		imageUrl: '',
		thumbnailUrl: '',
		description: '',
		published: false,
		scheduledFor: '',
		itemType: 'news',
		itemId: null
	};

	// Load users for selection
	async function loadUsers() {
		try {
			const response = await fetch('/api/admin/users?limit=100&status=active');
			const result = await response.json();
			
			if (response.ok && result.success) {
				users = result.data.users;
			} else {
				error = result.error || 'Failed to load users';
			}
		} catch (err) {
			console.error('Error loading users:', err);
			error = 'Failed to load users';
		}
	}

	// Get selected user details
	function getSelectedUser() {
		return users.find(user => user.id === selectedUserId);
	}

	// Handle content type change
	function handleContentTypeChange() {
		// Reset content data when changing type
		contentData = {
			title: '',
			content: '',
			imageUrl: '',
			thumbnailUrl: '',
			description: '',
			published: false,
			scheduledFor: '',
			itemType: selectedContentType === 'comment' ? 'news' : selectedContentType,
			itemId: null
		};
	}

	// Handle image selection
	function handleImageSelect(event: CustomEvent) {
		const { imageUrl, thumbnailUrl } = event.detail;
		contentData.imageUrl = imageUrl;
		if (thumbnailUrl) {
			contentData.thumbnailUrl = thumbnailUrl;
		}
	}

	// Validate form
	function validateForm(): boolean {
		if (!selectedUserId) {
			error = 'Please select a user to post as';
			return false;
		}

		if (!contentData.title && selectedContentType !== 'comment') {
			error = 'Title is required';
			return false;
		}

		if (!contentData.content) {
			error = 'Content is required';
			return false;
		}

		if (selectedContentType === 'gallery' && !contentData.imageUrl) {
			error = 'Image is required for gallery items';
			return false;
		}

		if (selectedContentType === 'comment' && !contentData.itemId) {
			error = 'Please select an item to comment on';
			return false;
		}

		return true;
	}

	// Post content as selected user
	async function postContent() {
		if (!validateForm()) return;

		isLoading = true;
		error = '';
		successMessage = '';

		try {
			const payload = {
				contentType: selectedContentType,
				asUserId: selectedUserId,
				contentData: contentData,
				scheduledFor: contentData.scheduledFor || null
			};

			const response = await fetch('/api/admin/post-as-user', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify(payload)
			});

			const result = await response.json();

			if (response.ok && result.success) {
				successMessage = result.message || 'Content posted successfully';
				
				// Reset form
				contentData = {
					title: '',
					content: '',
					imageUrl: '',
					thumbnailUrl: '',
					description: '',
					published: false,
					scheduledFor: '',
					itemType: selectedContentType === 'comment' ? 'news' : selectedContentType,
					itemId: null
				};
				selectedUserId = null;
			} else {
				error = result.error || 'Failed to post content';
			}
		} catch (err) {
			console.error('Error posting content:', err);
			error = 'An error occurred while posting content';
		} finally {
			isLoading = false;
		}
	}

	// Generate preview
	function generatePreview() {
		showPreview = !showPreview;
	}

	// Format date for display
	function formatDate(dateString: string): string {
		return new Date(dateString).toLocaleDateString('en-US', {
			year: 'numeric',
			month: 'short',
			day: 'numeric',
			hour: '2-digit',
			minute: '2-digit'
		});
	}

	// Initialize
	onMount(() => {
		loadUsers();
	});

	// Clear messages after 5 seconds
	$: if (successMessage) {
		setTimeout(() => {
			successMessage = '';
		}, 5000);
	}

	$: if (error) {
		setTimeout(() => {
			error = '';
		}, 8000);
	}
</script>

<svelte:head>
	<title>Post as User - FWFC Admin</title>
	<meta name="description" content="Post content on behalf of users" />
</svelte:head>

<div class="post-as-user">
	<header class="page-header">
		<div class="header-content">
			<h1>Post as User</h1>
			<p class="header-subtitle">
				Create content that appears to be posted by specific user accounts
			</p>
		</div>
		<div class="header-actions">
			<button
				class="btn secondary"
				onclick={() => goto('/admin')}
				disabled={isLoading}
			>
				← Back to Admin
			</button>
		</div>
	</header>

	<!-- Success/Error Messages -->
	{#if successMessage}
		<div class="message success" role="alert" aria-live="polite">
			✅ {successMessage}
		</div>
	{/if}

	{#if error}
		<ErrorMessage
			title="Error"
			message={error}
			type="error"
			dismissible={true}
			onDismiss={() => error = ''}
		/>
	{/if}

	<div class="content-form">
		<!-- User Selection -->
		<div class="form-section">
			<h3>Select User</h3>
			<div class="user-selection">
				<label for="user-select">Post as:</label>
				<select
					id="user-select"
					bind:value={selectedUserId}
					disabled={isLoading}
					required
				>
					<option value={null}>Select a user...</option>
					{#each users as user}
						<option value={user.id}>
							{user.displayName} (@{user.username})
							{#if user.isSimulated}🤖{/if}
						</option>
					{/each}
				</select>
				
				{#if selectedUserId}
					<div class="selected-user-info">
						{@const selectedUser = getSelectedUser()}
						{#if selectedUser}
							<div class="user-card">
								{#if selectedUser.avatarUrl}
									<img src={selectedUser.avatarUrl} alt="{selectedUser.displayName}" class="user-avatar" />
								{:else}
									<div class="user-avatar-placeholder">
										{selectedUser.displayName.charAt(0).toUpperCase()}
									</div>
								{/if}
								<div class="user-info">
									<h4>{selectedUser.displayName}</h4>
									<p>@{selectedUser.username}</p>
									{#if selectedUser.bio}
										<p class="user-bio">{selectedUser.bio}</p>
									{/if}
									<div class="user-meta">
										<span class="role-badge role-{selectedUser.role}">{selectedUser.role}</span>
										{#if selectedUser.isSimulated}
											<span class="simulated-badge">🤖 Simulated</span>
										{/if}
									</div>
								</div>
							</div>
						{/if}
					</div>
				{/if}
			</div>
		</div>

		<!-- Content Type Selection -->
		<div class="form-section">
			<h3>Content Type</h3>
			<div class="content-type-selection">
				{#each contentTypes as type}
					<label class="radio-option">
						<input
							type="radio"
							bind:group={selectedContentType}
							value={type.value}
							onchange={handleContentTypeChange}
							disabled={isLoading}
						/>
						<span class="radio-label">{type.label}</span>
					</label>
				{/each}
			</div>
		</div>

		<!-- Content Form -->
		{#if selectedUserId && selectedContentType}
			<div class="form-section">
				<h3>Content Details</h3>

				<!-- News Article Form -->
				{#if selectedContentType === 'news'}
					<div class="form-group">
						<label for="title">Title *</label>
						<input
							type="text"
							id="title"
							bind:value={contentData.title}
							placeholder="Enter article title"
							disabled={isLoading}
							required
						/>
					</div>

					<div class="form-group">
						<label>Featured Image</label>
						<ImageSelector
							selectedImageUrl={contentData.imageUrl}
							on:select={handleImageSelect}
							disabled={isLoading}
						/>
					</div>

					<div class="form-group">
						<label for="content">Content *</label>
						<RichTextEditor
							bind:value={contentData.content}
							placeholder="Write your article content here..."
							disabled={isLoading}
							height={400}
						/>
					</div>
				{/if}

				<!-- Gallery Item Form -->
				{#if selectedContentType === 'gallery'}
					<div class="form-group">
						<label for="title">Title *</label>
						<input
							type="text"
							id="title"
							bind:value={contentData.title}
							placeholder="Enter image title"
							disabled={isLoading}
							required
						/>
					</div>

					<div class="form-group">
						<label for="description">Description</label>
						<textarea
							id="description"
							bind:value={contentData.description}
							placeholder="Describe this image..."
							rows="3"
							disabled={isLoading}
						></textarea>
					</div>

					<div class="form-group">
						<label>Image *</label>
						<ImageSelector
							selectedImageUrl={contentData.imageUrl}
							on:select={handleImageSelect}
							disabled={isLoading}
						/>
					</div>
				{/if}

				<!-- Comment Form -->
				{#if selectedContentType === 'comment'}
					<div class="form-group">
						<label for="item-type">Comment on:</label>
						<select
							id="item-type"
							bind:value={contentData.itemType}
							disabled={isLoading}
						>
							<option value="news">News Article</option>
							<option value="gallery">Gallery Item</option>
						</select>
					</div>

					<div class="form-group">
						<label for="item-id">Item ID:</label>
						<input
							type="number"
							id="item-id"
							bind:value={contentData.itemId}
							placeholder="Enter the ID of the item to comment on"
							disabled={isLoading}
							required
						/>
					</div>

					<div class="form-group">
						<label for="content">Comment *</label>
						<textarea
							id="content"
							bind:value={contentData.content}
							placeholder="Write your comment here..."
							rows="4"
							disabled={isLoading}
							required
						></textarea>
					</div>
				{/if}

				<!-- Message Form -->
				{#if selectedContentType === 'message'}
					<div class="form-group">
						<label for="content">Message *</label>
						<textarea
							id="content"
							bind:value={contentData.content}
							placeholder="Write your message here..."
							rows="4"
							disabled={isLoading}
							required
						></textarea>
					</div>
				{/if}

				<!-- Publishing Options -->
				<div class="publishing-options">
					<h4>Publishing Options</h4>

					<div class="form-row">
						<div class="form-group">
							<label>
								<input
									type="checkbox"
									bind:checked={contentData.published}
									disabled={isLoading}
								/>
								Publish immediately
							</label>
						</div>

						<div class="form-group">
							<label for="scheduled-for">Schedule for later:</label>
							<input
								type="datetime-local"
								id="scheduled-for"
								bind:value={contentData.scheduledFor}
								disabled={isLoading || contentData.published}
							/>
						</div>
					</div>
				</div>

				<!-- Action Buttons -->
				<div class="form-actions">
					<button
						class="btn primary"
						onclick={postContent}
						disabled={isLoading}
					>
						{#if isLoading}
							<LoadingSpinner size="small" />
							Posting...
						{:else}
							{contentData.published ? 'Post Now' : 'Schedule Post'}
						{/if}
					</button>

					<button
						class="btn secondary"
						onclick={generatePreview}
						disabled={isLoading}
					>
						{showPreview ? 'Hide Preview' : 'Show Preview'}
					</button>
				</div>
			</div>
		{/if}

		<!-- Preview Section -->
		{#if showPreview && selectedUserId}
			<div class="form-section preview-section">
				<h3>Preview</h3>
				{@const selectedUser = getSelectedUser()}

				{#if selectedUser}
					<div class="content-preview">
						<div class="preview-header">
							<div class="preview-user">
								{#if selectedUser.avatarUrl}
									<img src={selectedUser.avatarUrl} alt="{selectedUser.displayName}" class="preview-avatar" />
								{:else}
									<div class="preview-avatar-placeholder">
										{selectedUser.displayName.charAt(0).toUpperCase()}
									</div>
								{/if}
								<div class="preview-user-info">
									<h5>{selectedUser.displayName}</h5>
									<p>@{selectedUser.username}</p>
									<small>
										{contentData.published ? 'Now' : contentData.scheduledFor ? formatDate(contentData.scheduledFor) : 'Draft'}
									</small>
								</div>
							</div>
						</div>

						<div class="preview-content">
							{#if selectedContentType === 'news'}
								<h4>{contentData.title || 'Untitled Article'}</h4>
								{#if contentData.imageUrl}
									<img src={contentData.imageUrl} alt="Featured image" class="preview-image" />
								{/if}
								<div class="preview-text">
									{@html contentData.content || '<em>No content yet...</em>'}
								</div>
							{:else if selectedContentType === 'gallery'}
								<h4>{contentData.title || 'Untitled Image'}</h4>
								{#if contentData.imageUrl}
									<img src={contentData.imageUrl} alt="Gallery image" class="preview-image" />
								{/if}
								{#if contentData.description}
									<p>{contentData.description}</p>
								{/if}
							{:else if selectedContentType === 'comment'}
								<p><strong>Comment on {contentData.itemType} #{contentData.itemId}:</strong></p>
								<p>{contentData.content || 'No comment yet...'}</p>
							{:else if selectedContentType === 'message'}
								<p>{contentData.content || 'No message yet...'}</p>
							{/if}
						</div>
					</div>
				{/if}
			</div>
		{/if}
	</div>
</div>

<style>
	.post-as-user {
		padding: 2rem;
		max-width: 1200px;
		margin: 0 auto;
		color: var(--theme-text-primary);
	}

	/* Page Header */
	.page-header {
		display: flex;
		justify-content: space-between;
		align-items: flex-start;
		margin-bottom: 2rem;
		padding-bottom: 1rem;
		border-bottom: 2px solid var(--theme-border);
	}

	.header-content h1 {
		margin: 0 0 0.5rem 0;
		font-size: 2.5rem;
		font-weight: 700;
		color: var(--theme-text-primary);
	}

	.header-subtitle {
		margin: 0;
		font-size: 1.1rem;
		color: var(--theme-text-secondary);
		line-height: 1.5;
	}

	.header-actions {
		display: flex;
		gap: 1rem;
		flex-wrap: wrap;
	}

	/* Messages */
	.message {
		padding: 1rem;
		border-radius: 8px;
		margin-bottom: 1.5rem;
		font-weight: 600;
	}

	.message.success {
		background-color: var(--theme-accent-success, #d4edda);
		color: var(--theme-accent-success-text, #155724);
		border: 1px solid var(--theme-accent-success-border, #c3e6cb);
	}

	/* Form Sections */
	.content-form {
		display: grid;
		gap: 2rem;
	}

	.form-section {
		border: 1px solid var(--theme-border);
		border-radius: 8px;
		padding: 1.5rem;
		background-color: var(--theme-card-bg);
	}

	.form-section h3 {
		margin: 0 0 1rem 0;
		color: var(--theme-text-primary);
		font-size: 1.2rem;
		border-bottom: 1px solid var(--theme-border);
		padding-bottom: 0.5rem;
	}

	/* User Selection */
	.user-selection label {
		display: block;
		font-weight: 600;
		margin-bottom: 0.5rem;
		color: var(--theme-text-primary);
	}

	.user-selection select {
		width: 100%;
		padding: 0.75rem;
		border: 2px solid var(--theme-input-border);
		border-radius: 6px;
		background-color: var(--theme-input-bg);
		color: var(--theme-text-primary);
		font-size: 1rem;
	}

	.selected-user-info {
		margin-top: 1rem;
	}

	.user-card {
		display: flex;
		gap: 1rem;
		padding: 1rem;
		border: 1px solid var(--theme-border);
		border-radius: 8px;
		background-color: var(--theme-bg-secondary);
	}

	.user-avatar {
		width: 60px;
		height: 60px;
		border-radius: 50%;
		object-fit: cover;
	}

	.user-avatar-placeholder {
		width: 60px;
		height: 60px;
		border-radius: 50%;
		background-color: var(--theme-accent-primary);
		color: white;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 1.5rem;
		font-weight: bold;
	}

	.user-info h4 {
		margin: 0 0 0.25rem 0;
		color: var(--theme-text-primary);
	}

	.user-info p {
		margin: 0 0 0.5rem 0;
		color: var(--theme-text-secondary);
	}

	.user-bio {
		font-style: italic;
		font-size: 0.9rem;
	}

	.user-meta {
		display: flex;
		gap: 0.5rem;
		align-items: center;
		flex-wrap: wrap;
	}

	.role-badge {
		padding: 0.25rem 0.5rem;
		border-radius: 4px;
		font-size: 0.8rem;
		font-weight: 600;
		text-transform: uppercase;
	}

	.role-admin {
		background-color: var(--theme-accent-danger, #dc3545);
		color: white;
	}

	.role-moderator {
		background-color: var(--theme-accent-warning, #ffc107);
		color: #212529;
	}

	.role-user {
		background-color: var(--theme-accent-info, #17a2b8);
		color: white;
	}

	.simulated-badge {
		padding: 0.25rem 0.5rem;
		border-radius: 4px;
		font-size: 0.8rem;
		background-color: var(--theme-accent-warning, #ffc107);
		color: #212529;
	}

	/* Content Type Selection */
	.content-type-selection {
		display: grid;
		grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
		gap: 1rem;
	}

	.radio-option {
		display: flex;
		align-items: center;
		gap: 0.5rem;
		padding: 1rem;
		border: 2px solid var(--theme-border);
		border-radius: 8px;
		cursor: pointer;
		transition: all 0.2s ease;
	}

	.radio-option:hover {
		border-color: var(--theme-accent-primary);
		background-color: var(--theme-bg-tertiary);
	}

	.radio-option input[type="radio"] {
		margin: 0;
	}

	.radio-option input[type="radio"]:checked + .radio-label {
		color: var(--theme-accent-primary);
		font-weight: 600;
	}

	.radio-option:has(input[type="radio"]:checked) {
		border-color: var(--theme-accent-primary);
		background-color: var(--theme-accent-primary-light, #e8f5e8);
	}

	.radio-label {
		font-size: 1rem;
		color: var(--theme-text-primary);
	}

	/* Form Elements */
	.form-group {
		margin-bottom: 1.5rem;
	}

	.form-group label {
		display: block;
		font-weight: 600;
		margin-bottom: 0.5rem;
		color: var(--theme-text-primary);
	}

	.form-group input,
	.form-group select,
	.form-group textarea {
		width: 100%;
		padding: 0.75rem;
		border: 2px solid var(--theme-input-border);
		border-radius: 6px;
		background-color: var(--theme-input-bg);
		color: var(--theme-text-primary);
		font-size: 1rem;
		font-family: inherit;
	}

	.form-group textarea {
		resize: vertical;
		min-height: 80px;
	}

	.form-group input:focus,
	.form-group select:focus,
	.form-group textarea:focus {
		outline: none;
		border-color: var(--theme-accent-primary);
		box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.1);
	}

	.form-row {
		display: grid;
		grid-template-columns: 1fr 1fr;
		gap: 1rem;
		margin-bottom: 1rem;
	}

	.publishing-options {
		margin-top: 2rem;
		padding-top: 1.5rem;
		border-top: 1px solid var(--theme-border);
	}

	.publishing-options h4 {
		margin: 0 0 1rem 0;
		color: var(--theme-text-primary);
		font-size: 1.1rem;
	}

	.form-actions {
		display: flex;
		gap: 1rem;
		margin-top: 2rem;
		padding-top: 1.5rem;
		border-top: 1px solid var(--theme-border);
	}

	/* Preview Section */
	.preview-section {
		border-color: var(--theme-accent-primary);
		background-color: rgba(76, 175, 80, 0.05);
	}

	.content-preview {
		border: 1px solid var(--theme-border);
		border-radius: 8px;
		background-color: var(--theme-card-bg);
		overflow: hidden;
	}

	.preview-header {
		padding: 1rem;
		border-bottom: 1px solid var(--theme-border);
		background-color: var(--theme-bg-secondary);
	}

	.preview-user {
		display: flex;
		gap: 1rem;
		align-items: center;
	}

	.preview-avatar {
		width: 40px;
		height: 40px;
		border-radius: 50%;
		object-fit: cover;
	}

	.preview-avatar-placeholder {
		width: 40px;
		height: 40px;
		border-radius: 50%;
		background-color: var(--theme-accent-primary);
		color: white;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 1rem;
		font-weight: bold;
	}

	.preview-user-info h5 {
		margin: 0;
		color: var(--theme-text-primary);
		font-size: 1rem;
	}

	.preview-user-info p {
		margin: 0;
		color: var(--theme-text-secondary);
		font-size: 0.9rem;
	}

	.preview-user-info small {
		color: var(--theme-text-secondary);
		font-size: 0.8rem;
	}

	.preview-content {
		padding: 1rem;
	}

	.preview-content h4 {
		margin: 0 0 1rem 0;
		color: var(--theme-text-primary);
	}

	.preview-image {
		max-width: 100%;
		height: auto;
		border-radius: 6px;
		margin: 1rem 0;
	}

	.preview-text {
		color: var(--theme-text-primary);
		line-height: 1.6;
	}

	/* Buttons */
	.btn {
		display: inline-flex;
		align-items: center;
		justify-content: center;
		gap: 0.5rem;
		padding: 0.75rem 1.5rem;
		border: 2px solid var(--theme-border);
		border-radius: 6px;
		font-size: 0.9rem;
		font-weight: 600;
		cursor: pointer;
		transition: all 0.2s ease;
		text-decoration: none;
		background-color: var(--theme-button-bg);
		color: var(--theme-button-text);
	}

	.btn:disabled {
		opacity: 0.6;
		cursor: not-allowed;
		transform: none !important;
	}

	.btn.secondary {
		background-color: var(--theme-button-bg);
		color: var(--theme-button-text);
		border-color: var(--theme-border);
	}

	.btn.primary {
		background-color: var(--theme-accent-primary);
		color: white;
		border-color: var(--theme-accent-primary);
	}

	.btn.primary:hover:not(:disabled) {
		background-color: var(--theme-accent-primary-hover);
		border-color: var(--theme-accent-primary-hover);
		transform: translateY(-1px);
	}

	.btn.secondary:hover:not(:disabled) {
		background-color: var(--theme-bg-tertiary);
		border-color: var(--theme-accent-primary);
		transform: translateY(-1px);
	}

	/* Responsive Design */
	@media (max-width: 768px) {
		.post-as-user {
			padding: 1rem;
		}

		.page-header {
			flex-direction: column;
			align-items: stretch;
			gap: 1rem;
		}

		.header-actions {
			justify-content: flex-start;
		}

		.content-type-selection {
			grid-template-columns: 1fr;
		}

		.user-card {
			flex-direction: column;
			text-align: center;
		}

		.form-row {
			grid-template-columns: 1fr;
		}

		.form-actions {
			flex-direction: column;
		}

		.preview-user {
			flex-direction: column;
			text-align: center;
		}
	}
</style>
