// See https://svelte.dev/docs/kit/types#app.d.ts
// for information about these interfaces
declare global {
	namespace App {
		// interface Error {}
		interface Locals {
			user?: {
				id: number;
				username: string;
				displayName: string;
				email: string;
				role: string;
				preferences?: {
					highContrast: boolean;
					largeText: boolean;
					simplifiedInterface: boolean;
				};
			};
			accessibility?: {
				highContrast: boolean;
				largeText: boolean;
				simplifiedInterface: boolean;
			};
		}
		// interface PageData {}
		// interface PageState {}
		// interface Platform {}
	}
}

export {};
