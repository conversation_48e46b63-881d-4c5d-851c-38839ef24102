<script lang="ts">
	import { onMount } from 'svelte';
	import { goto } from '$app/navigation';
	import LoadingSpinner from '$lib/components/LoadingSpinner.svelte';
	import ErrorMessage from '$lib/components/ErrorMessage.svelte';
	import type { PageData } from './$types';

	export let data: PageData;

	// DEBUG: Debugging utilities
	let debugCallCount = 0;
	let debugLog: string[] = [];

	function debugLogger(message: string, data?: any) {
		const timestamp = new Date().toISOString();
		const logEntry = `[${timestamp}] [${++debugCallCount}] ${message}`;
		console.log(logEntry, data || '');
		debugLog.push(logEntry);

		// Keep only last 50 entries to prevent memory issues
		if (debugLog.length > 50) {
			debugLog = debugLog.slice(-50);
		}
	}

	// DEBUG: Error boundary for reactive statements
	function safeReactiveExecution(name: string, fn: () => any) {
		try {
			debugLogger(`REACTIVE: ${name} - Starting execution`);
			const result = fn();
			debugLogger(`REACTIVE: ${name} - Completed successfully`);
			return result;
		} catch (error) {
			debugLogger(`REACTIVE: ${name} - ERROR:`, error);
			console.error(`Reactive statement error in ${name}:`, error);
			throw error;
		}
	}

	// State with debugging
	let _interactionTemplates: any[] = [];
	let _simulatedUsers: any[] = [];
	let _contentItems: any[] = [];
	let _isLoading = false;
	let _error = '';
	let _successMessage = '';

	// DEBUG: Reactive state getters/setters with logging
	$: interactionTemplates = safeReactiveExecution('interactionTemplates', () => _interactionTemplates);
	$: simulatedUsers = safeReactiveExecution('simulatedUsers', () => _simulatedUsers);
	$: contentItems = safeReactiveExecution('contentItems', () => {
		debugLogger('STATE: contentItems getter called', { length: _contentItems.length });
		return _contentItems;
	});
	$: isLoading = safeReactiveExecution('isLoading', () => _isLoading);
	$: error = safeReactiveExecution('error', () => _error);
	$: successMessage = safeReactiveExecution('successMessage', () => _successMessage);

	// Form state with debugging
	let _selectedTemplate: any = null;
	let _selectedUsers: number[] = [];
	let _selectedContentType = 'news';
	let _selectedContentId: number | null = null;
	let _customMessage = '';
	let _scheduledFor = '';
	let _generateVariations = true;
	let _maxInteractions = 5;

	// DEBUG: Form state getters/setters with logging
	$: selectedTemplate = safeReactiveExecution('selectedTemplate', () => _selectedTemplate);
	$: selectedUsers = safeReactiveExecution('selectedUsers', () => _selectedUsers);
	$: selectedContentType = safeReactiveExecution('selectedContentType', () => {
		debugLogger('STATE: selectedContentType getter called', _selectedContentType);
		return _selectedContentType;
	});
	$: selectedContentId = safeReactiveExecution('selectedContentId', () => {
		debugLogger('STATE: selectedContentId getter called', _selectedContentId);
		return _selectedContentId;
	});
	$: customMessage = safeReactiveExecution('customMessage', () => _customMessage);
	$: scheduledFor = safeReactiveExecution('scheduledFor', () => _scheduledFor);
	$: generateVariations = safeReactiveExecution('generateVariations', () => _generateVariations);
	$: maxInteractions = safeReactiveExecution('maxInteractions', () => _maxInteractions);

	// Content types
	const contentTypes = [
		{ value: 'news', label: 'News Articles' },
		{ value: 'gallery', label: 'Gallery Items' }
	];

	// Load data with debugging
	async function loadData() {
		debugLogger('FUNCTION: loadData - Starting');
		_isLoading = true;
		_error = ''; // Clear error immediately without timeout

		try {
			// Load interaction templates
			const templatesResponse = await fetch('/api/admin/interaction-templates');
			const templatesResult = await templatesResponse.json();

			if (templatesResponse.ok && templatesResult.success) {
				debugLogger('FUNCTION: loadData - Setting interactionTemplates', { count: templatesResult.data.length });
				_interactionTemplates = templatesResult.data;
			}

			// Load simulated users
			const usersResponse = await fetch('/api/admin/users?type=simulated&status=active&limit=100');
			const usersResult = await usersResponse.json();

			if (usersResponse.ok && usersResult.success) {
				debugLogger('FUNCTION: loadData - Setting simulatedUsers', { count: usersResult.data.users.length });
				_simulatedUsers = usersResult.data.users;
			}

			// Load content items
			await loadContentItems();

		} catch (err) {
			console.error('Error loading data:', err);
			debugLogger('FUNCTION: loadData - Error occurred', err);
			setErrorMessage('Failed to load data');
		} finally {
			debugLogger('FUNCTION: loadData - Completed');
			_isLoading = false;
		}
	}

	// Load content items based on selected type with debugging
	async function loadContentItems() {
		debugLogger('FUNCTION: loadContentItems - Starting', { selectedContentType: _selectedContentType });
		try {
			const response = await fetch(`/api/admin/content/${_selectedContentType}?published=true&limit=50`);
			const result = await response.json();

			if (response.ok && result.success) {
				debugLogger('FUNCTION: loadContentItems - Setting contentItems', { count: result.data.length });
				_contentItems = result.data;
			} else {
				debugLogger('FUNCTION: loadContentItems - API error', result);
			}
		} catch (err) {
			console.error('Error loading content items:', err);
			debugLogger('FUNCTION: loadContentItems - Exception', err);
		}
		debugLogger('FUNCTION: loadContentItems - Completed');
	}

	// Handle content type changes without reactive statements
	let previousContentType = _selectedContentType;
	let isInitialized = false;

	// Function to handle content type changes with debugging
	async function handleContentTypeChange() {
		debugLogger('FUNCTION: handleContentTypeChange - Called', {
			current: _selectedContentType,
			previous: previousContentType,
			isInitialized
		});

		if (_selectedContentType !== previousContentType) {
			debugLogger('FUNCTION: handleContentTypeChange - Content type changed, updating');
			_selectedContentId = null;
			previousContentType = _selectedContentType;
			await loadContentItems();
		} else {
			debugLogger('FUNCTION: handleContentTypeChange - No change detected');
		}
	}

	// Toggle user selection with debugging
	function toggleUserSelection(userId: number) {
		debugLogger('FUNCTION: toggleUserSelection - Called', { userId, currentSelection: _selectedUsers });
		if (_selectedUsers.includes(userId)) {
			_selectedUsers = _selectedUsers.filter(id => id !== userId);
		} else {
			_selectedUsers = [..._selectedUsers, userId];
		}
		debugLogger('FUNCTION: toggleUserSelection - Updated', { newSelection: _selectedUsers });
	}

	// Select all simulated users with debugging
	function selectAllUsers() {
		debugLogger('FUNCTION: selectAllUsers - Called', {
			currentCount: _selectedUsers.length,
			totalUsers: _simulatedUsers.length
		});
		if (_selectedUsers.length === _simulatedUsers.length) {
			_selectedUsers = [];
		} else {
			_selectedUsers = _simulatedUsers.map(user => user.id);
		}
		debugLogger('FUNCTION: selectAllUsers - Updated', { newSelection: _selectedUsers });
	}

	// Safely parse JSON with fallback
	function safeJsonParse(jsonString: string | null | undefined, fallback: any = []): any {
		if (!jsonString) return fallback;

		try {
			return JSON.parse(jsonString);
		} catch (error) {
			console.warn('Failed to parse JSON:', jsonString, error);
			return fallback;
		}
	}

	// Get template preview
	function getTemplatePreview(template: any): string {
		if (!template) return '';

		let preview = template.template;
		const variables = safeJsonParse(template.variables, []);

		// Replace variables with example values
		variables.forEach((variable: string) => {
			const placeholder = `{${variable}}`;
			switch (variable) {
				case 'username':
					preview = preview.replace(placeholder, 'JohnDoe');
					break;
				case 'topic':
					preview = preview.replace(placeholder, 'this amazing content');
					break;
				default:
					preview = preview.replace(placeholder, `[${variable}]`);
			}
		});

		return preview;
	}

	// Generate interactions
	async function generateInteractions() {
		if (!selectedTemplate && !customMessage) {
			setErrorMessage('Please select a template or enter a custom message');
			return;
		}

		if (selectedUsers.length === 0) {
			setErrorMessage('Please select at least one user');
			return;
		}

		if (!selectedContentId) {
			setErrorMessage('Please select content to interact with');
			return;
		}

		isLoading = true;
		error = ''; // Clear error immediately
		successMessage = ''; // Clear success immediately

		try {
			const payload = {
				templateId: selectedTemplate?.id || null,
				customMessage: customMessage || null,
				userIds: selectedUsers,
				contentType: selectedContentType,
				contentId: selectedContentId,
				generateVariations,
				maxInteractions,
				scheduledFor: scheduledFor || null
			};

			const response = await fetch('/api/admin/simulate-interactions', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify(payload)
			});

			const result = await response.json();

			if (response.ok && result.success) {
				setSuccessMessage(result.message || 'Interactions generated successfully');

				// Reset form
				selectedTemplate = null;
				selectedUsers = [];
				selectedContentId = null;
				customMessage = '';
				scheduledFor = '';
			} else {
				setErrorMessage(result.error || 'Failed to generate interactions');
			}
		} catch (err) {
			console.error('Error generating interactions:', err);
			setErrorMessage('An error occurred while generating interactions');
		} finally {
			isLoading = false;
		}
	}

	// Get selected content item - reactive with debugging
	$: selectedContentItem = safeReactiveExecution('selectedContentItem', () => {
		const result = _contentItems.find(item => item.id === _selectedContentId);
		debugLogger('REACTIVE: selectedContentItem computed', {
			selectedContentId: _selectedContentId,
			contentItemsLength: _contentItems.length,
			found: !!result
		});
		return result;
	});

	// Format date for display
	function formatDate(dateString: string): string {
		return new Date(dateString).toLocaleDateString('en-US', {
			year: 'numeric',
			month: 'short',
			day: 'numeric'
		});
	}

	// Initialize with debugging
	onMount(async () => {
		debugLogger('LIFECYCLE: onMount - Starting');

		// Add global error handler for reactivity errors
		window.addEventListener('error', (event) => {
			debugLogger('GLOBAL ERROR:', event.error);
			if (event.error?.message?.includes('effect_update_depth_exceeded')) {
				debugLogger('REACTIVITY ERROR DETECTED - Stack trace:', event.error.stack);
			}
		});

		window.addEventListener('unhandledrejection', (event) => {
			debugLogger('UNHANDLED PROMISE REJECTION:', event.reason);
		});

		await loadData();
		// Set initialized flag after initial load
		isInitialized = true;
		debugLogger('LIFECYCLE: onMount - Completed', { isInitialized });
	});

	// Clear messages after delays - using proper effect management
	let successMessageTimeout: NodeJS.Timeout | null = null;
	let errorMessageTimeout: NodeJS.Timeout | null = null;

	// Function to set success message with auto-clear and debugging
	function setSuccessMessage(message: string) {
		debugLogger('FUNCTION: setSuccessMessage - Called', { message });
		_successMessage = message;
		if (successMessageTimeout) {
			clearTimeout(successMessageTimeout);
		}
		successMessageTimeout = setTimeout(() => {
			debugLogger('FUNCTION: setSuccessMessage - Auto-clearing');
			_successMessage = '';
			successMessageTimeout = null;
		}, 5000);
	}

	// Function to set error message with auto-clear and debugging
	function setErrorMessage(message: string) {
		debugLogger('FUNCTION: setErrorMessage - Called', { message });
		_error = message;
		if (errorMessageTimeout) {
			clearTimeout(errorMessageTimeout);
		}
		errorMessageTimeout = setTimeout(() => {
			debugLogger('FUNCTION: setErrorMessage - Auto-clearing');
			_error = '';
			errorMessageTimeout = null;
		}, 8000);
	}
</script>

<svelte:head>
	<title>Simulate Interactions - FWFC Admin</title>
	<meta name="description" content="Generate simulated user interactions and engagement" />
</svelte:head>

<div class="simulate-interactions">
	<!-- DEBUG PANEL -->
	<div class="debug-panel" style="background: #f0f0f0; border: 1px solid #ccc; padding: 1rem; margin-bottom: 1rem; max-height: 200px; overflow-y: auto;">
		<h4>🐛 Debug Log (Last {debugLog.length} entries)</h4>
		<button onclick={() => { debugLog = []; debugCallCount = 0; }} style="margin-bottom: 0.5rem;">Clear Log</button>
		<div style="font-family: monospace; font-size: 0.8rem;">
			{#each debugLog.slice(-20) as logEntry}
				<div>{logEntry}</div>
			{/each}
		</div>
	</div>

	<header class="page-header">
		<div class="header-content">
			<h1>Simulate User Interactions</h1>
			<p class="header-subtitle">
				Generate natural-looking user interactions using templates and simulated accounts
			</p>
		</div>
		<div class="header-actions">
			<button
				class="btn secondary"
				onclick={() => goto('/admin/interaction-templates')}
				disabled={isLoading}
			>
				📝 Manage Templates
			</button>
			<button
				class="btn secondary"
				onclick={() => goto('/admin')}
				disabled={isLoading}
			>
				← Back to Admin
			</button>
		</div>
	</header>

	<!-- Success/Error Messages -->
	{#if successMessage}
		<div class="message success" role="alert" aria-live="polite">
			✅ {successMessage}
		</div>
	{/if}

	{#if error}
		<ErrorMessage
			title="Error"
			message={error}
			type="error"
			dismissible={true}
			onDismiss={() => {
				error = '';
				if (errorMessageTimeout) {
					clearTimeout(errorMessageTimeout);
					errorMessageTimeout = null;
				}
			}}
		/>
	{/if}

	{#if isLoading}
		<div class="loading-container">
			<LoadingSpinner />
			<p>Loading simulation data...</p>
		</div>
	{:else}
		<div class="simulation-form">
			<!-- Template Selection -->
			<div class="form-section">
				<h3>Interaction Template</h3>
				<div class="template-selection">
					<div class="template-grid">
						{#each interactionTemplates as template}
							<label class="template-option" class:selected={selectedTemplate?.id === template.id}>
								<input
									type="radio"
									bind:group={_selectedTemplate}
									value={template}
									disabled={isLoading}
									onchange={() => debugLogger('BIND: selectedTemplate changed', template)}
								/>
								<div class="template-content">
									<h4>{template.name}</h4>
									<p class="template-category">{template.category}</p>
									<p class="template-preview">{getTemplatePreview(template)}</p>
									<div class="template-meta">
										<span class="personality-tags">
											{#each safeJsonParse(template.personality, []) as trait}
												<span class="trait-tag">{trait}</span>
											{/each}
										</span>
									</div>
								</div>
							</label>
						{/each}
					</div>

					<div class="custom-message-section">
						<h4>Or use custom message:</h4>
						<textarea
							bind:value={_customMessage}
							placeholder="Enter a custom interaction message..."
							rows="3"
							disabled={isLoading}
							oninput={() => debugLogger('BIND: customMessage changed', _customMessage)}
						></textarea>
						<small class="help-text">
							Use variables like {'{username}'} and {'{topic}'} for personalization
						</small>
					</div>
				</div>
			</div>

			<!-- Content Selection -->
			<div class="form-section">
				<h3>Target Content</h3>
				<div class="content-selection">
					<div class="form-row">
						<div class="form-group">
							<label for="content-type">Content Type:</label>
							<select
								id="content-type"
								bind:value={_selectedContentType}
								onchange={() => {
									debugLogger('BIND: selectedContentType changed', _selectedContentType);
									handleContentTypeChange();
								}}
								disabled={isLoading}
							>
								{#each contentTypes as type}
									<option value={type.value}>{type.label}</option>
								{/each}
							</select>
						</div>

						<div class="form-group">
							<label for="content-item">Content Item:</label>
							<select
								id="content-item"
								bind:value={_selectedContentId}
								onchange={() => debugLogger('BIND: selectedContentId changed', _selectedContentId)}
								disabled={isLoading}
							>
								<option value={null}>Select content...</option>
								{#each contentItems as item}
									<option value={item.id}>
										{item.title} - {formatDate(item.createdAt)}
									</option>
								{/each}
							</select>
						</div>
					</div>

					{#if selectedContentId && selectedContentItem}
						<div class="content-preview">
							<h4>Selected Content:</h4>
							<div class="content-card">
								<h5>{selectedContentItem.title}</h5>
								{#if selectedContentItem.imageUrl}
									<img src={selectedContentItem.imageUrl} alt={selectedContentItem.title} class="content-image" />
								{/if}
								<p class="content-excerpt">
									{selectedContentItem.content?.substring(0, 200) || selectedContentItem.description?.substring(0, 200) || 'No description available'}...
								</p>
								<small>By {selectedContentItem.author?.displayName || 'Unknown'} on {formatDate(selectedContentItem.createdAt)}</small>
							</div>
						</div>
					{/if}
				</div>
			</div>

			<!-- User Selection -->
			<div class="form-section">
				<h3>Simulated Users</h3>
				<div class="user-selection">
					<div class="selection-header">
						<button
							type="button"
							class="btn secondary"
							onclick={selectAllUsers}
							disabled={isLoading}
						>
							{selectedUsers.length === simulatedUsers.length ? 'Deselect All' : 'Select All'}
						</button>
						<span class="selection-count">
							{selectedUsers.length} of {simulatedUsers.length} users selected
						</span>
					</div>

					<div class="users-grid">
						{#each simulatedUsers as user}
							<label class="user-option" class:selected={selectedUsers.includes(user.id)}>
								<input
									type="checkbox"
									checked={selectedUsers.includes(user.id)}
									onchange={() => toggleUserSelection(user.id)}
									disabled={isLoading}
								/>
								<div class="user-card">
									{#if user.avatarUrl}
										<img src={user.avatarUrl} alt={user.displayName} class="user-avatar" />
									{:else}
										<div class="user-avatar-placeholder">
											{user.displayName.charAt(0).toUpperCase()}
										</div>
									{/if}
									<div class="user-info">
										<h4>{user.displayName}</h4>
										<p>@{user.username}</p>
										{#if user.bio}
											<p class="user-bio">{user.bio}</p>
										{/if}
										{#if user.simulatedPersonality}
											{@const personality = safeJsonParse(user.simulatedPersonality, {})}
											<div class="personality-info">
												{#if personality.traits && Array.isArray(personality.traits)}
													<div class="traits">
														{#each personality.traits as trait}
															<span class="trait-tag">{trait}</span>
														{/each}
													</div>
												{/if}
											</div>
										{/if}
									</div>
								</div>
							</label>
						{/each}
					</div>
				</div>
			</div>

			<!-- Generation Options -->
			<div class="form-section">
				<h3>Generation Options</h3>
				<div class="generation-options">
					<div class="form-row">
						<div class="form-group">
							<label>
								<input
									type="checkbox"
									bind:checked={_generateVariations}
									onchange={() => debugLogger('BIND: generateVariations changed', _generateVariations)}
									disabled={isLoading}
								/>
								Generate variations of the template
							</label>
							<small class="help-text">
								Create slightly different versions of the message for more natural interactions
							</small>
						</div>

						<div class="form-group">
							<label for="max-interactions">Maximum interactions per user:</label>
							<input
								type="number"
								id="max-interactions"
								bind:value={_maxInteractions}
								oninput={() => debugLogger('BIND: maxInteractions changed', _maxInteractions)}
								min="1"
								max="10"
								disabled={isLoading}
							/>
							<small class="help-text">
								Limit the number of interactions each user can generate
							</small>
						</div>
					</div>

					<div class="form-group">
						<label for="scheduled-for">Schedule for later (optional):</label>
						<input
							type="datetime-local"
							id="scheduled-for"
							bind:value={_scheduledFor}
							oninput={() => debugLogger('BIND: scheduledFor changed', _scheduledFor)}
							disabled={isLoading}
						/>
						<small class="help-text">
							Leave empty to generate interactions immediately
						</small>
					</div>
				</div>
			</div>

			<!-- Generate Button -->
			<div class="form-section">
				<div class="generate-section">
					<button
						class="btn primary large"
						onclick={generateInteractions}
						disabled={isLoading || (!selectedTemplate && !customMessage) || selectedUsers.length === 0 || !selectedContentId}
					>
						{#if isLoading}
							<LoadingSpinner size="small" />
							Generating...
						{:else}
							🎭 Generate Interactions
						{/if}
					</button>

					<div class="generation-summary">
						<h4>Generation Summary:</h4>
						<ul>
							<li><strong>Template:</strong> {selectedTemplate?.name || (customMessage ? 'Custom Message' : 'None selected')}</li>
							<li><strong>Users:</strong> {selectedUsers.length} selected</li>
							<li><strong>Content:</strong> {selectedContentType} {selectedContentId ? `#${selectedContentId}` : '(none selected)'}</li>
							<li><strong>Timing:</strong> {scheduledFor ? `Scheduled for ${new Date(scheduledFor).toLocaleString()}` : 'Immediate'}</li>
							<li><strong>Variations:</strong> {generateVariations ? 'Enabled' : 'Disabled'}</li>
							<li><strong>Max per user:</strong> {maxInteractions}</li>
						</ul>
					</div>
				</div>
			</div>
		</div>
	{/if}
</div>

<style>
	.simulate-interactions {
		padding: 2rem;
		max-width: 1400px;
		margin: 0 auto;
		color: var(--theme-text-primary);
	}

	/* Page Header */
	.page-header {
		display: flex;
		justify-content: space-between;
		align-items: flex-start;
		margin-bottom: 2rem;
		padding-bottom: 1rem;
		border-bottom: 2px solid var(--theme-border);
	}

	.header-content h1 {
		margin: 0 0 0.5rem 0;
		font-size: 2.5rem;
		font-weight: 700;
		color: var(--theme-text-primary);
	}

	.header-subtitle {
		margin: 0;
		font-size: 1.1rem;
		color: var(--theme-text-secondary);
		line-height: 1.5;
	}

	.header-actions {
		display: flex;
		gap: 1rem;
		flex-wrap: wrap;
	}

	/* Messages */
	.message {
		padding: 1rem;
		border-radius: 8px;
		margin-bottom: 1.5rem;
		font-weight: 600;
	}

	.message.success {
		background-color: var(--theme-accent-success, #d4edda);
		color: var(--theme-accent-success-text, #155724);
		border: 1px solid var(--theme-accent-success-border, #c3e6cb);
	}

	/* Loading */
	.loading-container {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 4rem 2rem;
		gap: 1rem;
	}

	.loading-container p {
		color: var(--theme-text-secondary);
		font-size: 1.1rem;
	}

	/* Form Sections */
	.simulation-form {
		display: grid;
		gap: 2rem;
	}

	.form-section {
		border: 1px solid var(--theme-border);
		border-radius: 8px;
		padding: 1.5rem;
		background-color: var(--theme-card-bg);
	}

	.form-section h3 {
		margin: 0 0 1rem 0;
		color: var(--theme-text-primary);
		font-size: 1.2rem;
		border-bottom: 1px solid var(--theme-border);
		padding-bottom: 0.5rem;
	}

	/* Template Selection */
	.template-grid {
		display: grid;
		grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
		gap: 1rem;
		margin-bottom: 2rem;
	}

	.template-option {
		display: block;
		border: 2px solid var(--theme-border);
		border-radius: 8px;
		padding: 1rem;
		cursor: pointer;
		transition: all 0.2s ease;
		background-color: var(--theme-bg-secondary);
	}

	.template-option:hover {
		border-color: var(--theme-accent-primary);
		background-color: var(--theme-bg-tertiary);
	}

	.template-option.selected {
		border-color: var(--theme-accent-primary);
		background-color: var(--theme-accent-primary-light, #e8f5e8);
	}

	.template-option input[type="radio"] {
		display: none;
	}

	.template-content h4 {
		margin: 0 0 0.5rem 0;
		color: var(--theme-text-primary);
		font-size: 1.1rem;
	}

	.template-category {
		margin: 0 0 0.5rem 0;
		color: var(--theme-text-secondary);
		font-size: 0.9rem;
		text-transform: uppercase;
		letter-spacing: 0.5px;
	}

	.template-preview {
		margin: 0 0 1rem 0;
		color: var(--theme-text-primary);
		font-style: italic;
		line-height: 1.4;
		background-color: var(--theme-bg-tertiary);
		padding: 0.75rem;
		border-radius: 4px;
		border-left: 3px solid var(--theme-accent-primary);
	}

	.personality-tags {
		display: flex;
		gap: 0.5rem;
		flex-wrap: wrap;
	}

	.trait-tag {
		padding: 0.25rem 0.5rem;
		background-color: var(--theme-accent-info, #17a2b8);
		color: white;
		border-radius: 12px;
		font-size: 0.8rem;
		font-weight: 600;
	}

	.custom-message-section {
		border-top: 1px solid var(--theme-border);
		padding-top: 1.5rem;
	}

	.custom-message-section h4 {
		margin: 0 0 1rem 0;
		color: var(--theme-text-primary);
	}

	.custom-message-section textarea {
		width: 100%;
		padding: 0.75rem;
		border: 2px solid var(--theme-input-border);
		border-radius: 6px;
		background-color: var(--theme-input-bg);
		color: var(--theme-text-primary);
		font-size: 1rem;
		font-family: inherit;
		resize: vertical;
	}

	.help-text {
		display: block;
		margin-top: 0.5rem;
		color: var(--theme-text-secondary);
		font-size: 0.85rem;
	}

	/* Form Elements */
	.form-row {
		display: grid;
		grid-template-columns: 1fr 1fr;
		gap: 1rem;
		margin-bottom: 1rem;
	}

	.form-group {
		display: flex;
		flex-direction: column;
		gap: 0.5rem;
	}

	.form-group label {
		font-weight: 600;
		color: var(--theme-text-primary);
		font-size: 0.9rem;
	}

	.form-group select {
		padding: 0.75rem;
		border: 2px solid var(--theme-input-border);
		border-radius: 6px;
		background-color: var(--theme-input-bg);
		color: var(--theme-text-primary);
		font-size: 1rem;
	}

	/* Content Preview */
	.content-preview {
		margin-top: 1rem;
		padding-top: 1rem;
		border-top: 1px solid var(--theme-border);
	}

	.content-preview h4 {
		margin: 0 0 1rem 0;
		color: var(--theme-text-primary);
	}

	.content-card {
		border: 1px solid var(--theme-border);
		border-radius: 6px;
		padding: 1rem;
		background-color: var(--theme-bg-secondary);
	}

	.content-card h5 {
		margin: 0 0 0.5rem 0;
		color: var(--theme-text-primary);
	}

	.content-image {
		max-width: 200px;
		height: auto;
		border-radius: 4px;
		margin: 0.5rem 0;
	}

	.content-excerpt {
		margin: 0.5rem 0;
		color: var(--theme-text-secondary);
		line-height: 1.4;
	}

	.content-card small {
		color: var(--theme-text-secondary);
		font-size: 0.85rem;
	}

	/* User Selection */
	.selection-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 1rem;
		padding-bottom: 1rem;
		border-bottom: 1px solid var(--theme-border);
	}

	.selection-count {
		color: var(--theme-text-secondary);
		font-weight: 600;
	}

	.users-grid {
		display: grid;
		grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
		gap: 1rem;
	}

	.user-option {
		display: block;
		border: 2px solid var(--theme-border);
		border-radius: 8px;
		padding: 1rem;
		cursor: pointer;
		transition: all 0.2s ease;
		background-color: var(--theme-bg-secondary);
	}

	.user-option:hover {
		border-color: var(--theme-accent-primary);
		background-color: var(--theme-bg-tertiary);
	}

	.user-option.selected {
		border-color: var(--theme-accent-primary);
		background-color: var(--theme-accent-primary-light, #e8f5e8);
	}

	.user-option input[type="checkbox"] {
		display: none;
	}

	.user-card {
		display: flex;
		gap: 1rem;
		align-items: flex-start;
	}

	.user-avatar {
		width: 50px;
		height: 50px;
		border-radius: 50%;
		object-fit: cover;
		flex-shrink: 0;
	}

	.user-avatar-placeholder {
		width: 50px;
		height: 50px;
		border-radius: 50%;
		background-color: var(--theme-accent-primary);
		color: white;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 1.2rem;
		font-weight: bold;
		flex-shrink: 0;
	}

	.user-info {
		flex: 1;
		min-width: 0;
	}

	.user-info h4 {
		margin: 0 0 0.25rem 0;
		color: var(--theme-text-primary);
		font-size: 1rem;
	}

	.user-info p {
		margin: 0 0 0.5rem 0;
		color: var(--theme-text-secondary);
		font-size: 0.9rem;
	}

	.user-bio {
		font-style: italic;
		font-size: 0.85rem !important;
		line-height: 1.3;
	}

	.personality-info {
		margin-top: 0.5rem;
	}

	.traits {
		display: flex;
		gap: 0.25rem;
		flex-wrap: wrap;
	}

	/* Generation Options */
	.generation-options .form-group {
		margin-bottom: 1.5rem;
	}

	.generation-options input[type="number"] {
		padding: 0.75rem;
		border: 2px solid var(--theme-input-border);
		border-radius: 6px;
		background-color: var(--theme-input-bg);
		color: var(--theme-text-primary);
		font-size: 1rem;
		width: 100px;
	}

	.generation-options input[type="datetime-local"] {
		padding: 0.75rem;
		border: 2px solid var(--theme-input-border);
		border-radius: 6px;
		background-color: var(--theme-input-bg);
		color: var(--theme-text-primary);
		font-size: 1rem;
	}

	.generation-options label {
		display: flex;
		align-items: center;
		gap: 0.5rem;
		font-weight: 600;
		color: var(--theme-text-primary);
		cursor: pointer;
	}

	.generation-options input[type="checkbox"] {
		margin: 0;
	}

	/* Generate Section */
	.generate-section {
		text-align: center;
	}

	.btn.large {
		padding: 1rem 2rem;
		font-size: 1.1rem;
		margin-bottom: 2rem;
	}

	.btn.primary {
		background-color: var(--theme-accent-primary);
		color: white;
		border-color: var(--theme-accent-primary);
	}

	.btn.primary:hover:not(:disabled) {
		background-color: var(--theme-accent-primary-hover);
		border-color: var(--theme-accent-primary-hover);
		transform: translateY(-1px);
	}

	.generation-summary {
		text-align: left;
		max-width: 500px;
		margin: 0 auto;
		padding: 1.5rem;
		border: 1px solid var(--theme-border);
		border-radius: 8px;
		background-color: var(--theme-bg-secondary);
	}

	.generation-summary h4 {
		margin: 0 0 1rem 0;
		color: var(--theme-text-primary);
		text-align: center;
	}

	.generation-summary ul {
		margin: 0;
		padding: 0;
		list-style: none;
	}

	.generation-summary li {
		margin-bottom: 0.5rem;
		color: var(--theme-text-secondary);
		line-height: 1.4;
	}

	.generation-summary li strong {
		color: var(--theme-text-primary);
	}

	/* Buttons */
	.btn {
		display: inline-flex;
		align-items: center;
		justify-content: center;
		gap: 0.5rem;
		padding: 0.75rem 1.5rem;
		border: 2px solid var(--theme-border);
		border-radius: 6px;
		font-size: 0.9rem;
		font-weight: 600;
		cursor: pointer;
		transition: all 0.2s ease;
		text-decoration: none;
		background-color: var(--theme-button-bg);
		color: var(--theme-button-text);
	}

	.btn:disabled {
		opacity: 0.6;
		cursor: not-allowed;
		transform: none !important;
	}

	.btn.secondary {
		background-color: var(--theme-button-bg);
		color: var(--theme-button-text);
		border-color: var(--theme-border);
	}

	.btn.secondary:hover:not(:disabled) {
		background-color: var(--theme-bg-tertiary);
		border-color: var(--theme-accent-primary);
		transform: translateY(-1px);
	}

	/* Responsive Design */
	@media (max-width: 768px) {
		.simulate-interactions {
			padding: 1rem;
		}

		.page-header {
			flex-direction: column;
			align-items: stretch;
			gap: 1rem;
		}

		.header-actions {
			justify-content: flex-start;
		}

		.template-grid {
			grid-template-columns: 1fr;
		}

		.form-row {
			grid-template-columns: 1fr;
		}

		.users-grid {
			grid-template-columns: 1fr;
		}

		.user-card {
			flex-direction: column;
			text-align: center;
		}

		.selection-header {
			flex-direction: column;
			gap: 1rem;
			text-align: center;
		}

		.generation-summary {
			margin: 0;
		}
	}
</style>
