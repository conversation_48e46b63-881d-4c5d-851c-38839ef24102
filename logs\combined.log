{"level":"debug","message":"No session cookie found","timestamp":"2025-05-16 11:10:50:1050"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-05-16 11:10:50:1050"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-05-16 11:11:03:113"}
{"email":"<EMAIL>","level":"info","message":"Development mode: Admin login successful","timestamp":"2025-05-16 11:11:03:113"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:11:04:114"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:11:08:118"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:11:08:118"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:11:10:1110"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:11:13:1113"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:11:14:1114"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:14:47:1447"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:14:48:1448"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:14:48:1448"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:14:48:1448"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:14:49:1449"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:14:49:1449"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:14:49:1449"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:14:49:1449"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:14:50:1450"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:14:50:1450"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:14:51:1451"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:15:00:150"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:15:05:155"}
{"email":"<EMAIL>","level":"info","message":"Development mode: Admin login successful","timestamp":"2025-05-16 11:15:05:155"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:15:05:155"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:15:08:158"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:15:08:158"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:15:12:1512"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:15:12:1512"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:15:12:1512"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:15:12:1512"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:15:14:1514"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:15:15:1515"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:15:17:1517"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:15:17:1517"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:15:18:1518"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:15:20:1520"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:15:20:1520"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:15:21:1521"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:15:22:1522"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:16:41:1641"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:16:41:1641"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:16:42:1642"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:16:42:1642"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:16:42:1642"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:16:42:1642"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:16:42:1642"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:16:42:1642"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:16:43:1643"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:16:44:1644"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:16:44:1644"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:16:44:1644"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:16:51:1651"}
{"email":"<EMAIL>","level":"info","message":"Development mode: Admin login successful","timestamp":"2025-05-16 11:16:51:1651"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:16:51:1651"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:16:54:1654"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:16:57:1657"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:17:01:171"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:17:04:174"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:17:04:174"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:17:06:176"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:17:06:176"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:17:06:176"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:17:06:176"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:17:07:177"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:20:30:2030"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:20:52:2052"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:20:57:2057"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:21:04:214"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:21:12:2112"}
{"email":"<EMAIL>","level":"info","message":"Development mode: Admin login successful","timestamp":"2025-05-16 11:21:12:2112"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:21:12:2112"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:21:14:2114"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:21:16:2116"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:21:18:2118"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:21:18:2118"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:21:18:2118"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:21:20:2120"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:21:20:2120"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:21:22:2122"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:21:23:2123"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:21:24:2124"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:21:41:2141"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:21:52:2152"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:21:52:2152"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:21:54:2154"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:21:54:2154"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:21:58:2158"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:22:31:2231"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:22:31:2231"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:22:38:2238"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:22:38:2238"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:22:38:2238"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:22:40:2240"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:22:40:2240"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:22:41:2241"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:22:41:2241"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:22:42:2242"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:22:43:2243"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:22:44:2244"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:22:44:2244"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:22:46:2246"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:22:46:2246"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:22:47:2247"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:22:48:2248"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:22:55:2255"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:22:55:2255"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:22:57:2257"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:22:57:2257"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:23:00:230"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:23:01:231"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:23:01:231"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:23:18:2318"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:23:20:2320"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:23:50:2350"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:23:51:2351"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:23:51:2351"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:23:53:2353"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:23:54:2354"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:23:54:2354"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:23:55:2355"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:24:07:247"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:24:07:247"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:24:09:249"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:24:09:249"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:24:28:2428"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:24:28:2428"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:24:33:2433"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:24:34:2434"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:24:35:2435"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:25:18:2518"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:25:37:2537"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-16 11:25:38:2538"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-05-29 14:31:58:3158"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-05-29 14:32:06:326"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-05-29 14:33:18:3318"}
{"email":"<EMAIL>","level":"info","message":"Development mode: Admin login successful","timestamp":"2025-05-29 14:33:18:3318"}
{"email":"<EMAIL>","level":"debug","message":"Using mock admin user","timestamp":"2025-05-29 14:33:18:3318"}
{"email":"<EMAIL>","level":"debug","message":"Using mock admin user","timestamp":"2025-05-29 14:33:22:3322"}
{"email":"<EMAIL>","level":"debug","message":"Using mock admin user","timestamp":"2025-05-29 14:51:22:5122"}
{"email":"<EMAIL>","level":"debug","message":"Using mock admin user","timestamp":"2025-05-29 14:51:25:5125"}
{"email":"<EMAIL>","level":"debug","message":"Using mock admin user","timestamp":"2025-05-29 14:56:07:567"}
{"email":"<EMAIL>","level":"debug","message":"Using mock admin user","timestamp":"2025-05-29 15:03:22:322"}
{"email":"<EMAIL>","level":"debug","message":"Using mock admin user","timestamp":"2025-05-29 15:03:29:329"}
{"email":"<EMAIL>","level":"debug","message":"Using mock admin user","timestamp":"2025-05-29 15:03:44:344"}
{"email":"<EMAIL>","level":"debug","message":"Using mock admin user","timestamp":"2025-05-29 15:03:48:348"}
{"email":"<EMAIL>","level":"debug","message":"Using mock admin user","timestamp":"2025-05-29 15:03:49:349"}
{"email":"<EMAIL>","level":"debug","message":"Using mock admin user","timestamp":"2025-05-29 15:03:53:353"}
{"email":"<EMAIL>","level":"debug","message":"Using mock admin user","timestamp":"2025-05-29 15:05:43:543"}
{"email":"<EMAIL>","level":"debug","message":"Using mock admin user","timestamp":"2025-05-29 15:05:45:545"}
{"email":"<EMAIL>","level":"debug","message":"Using mock admin user","timestamp":"2025-05-29 15:06:51:651"}
{"email":"<EMAIL>","level":"debug","message":"Using mock admin user","timestamp":"2025-05-29 15:06:54:654"}
{"email":"<EMAIL>","level":"debug","message":"Using mock admin user","timestamp":"2025-05-29 15:07:01:71"}
{"email":"<EMAIL>","level":"debug","message":"Using mock admin user","timestamp":"2025-05-29 15:07:03:73"}
{"email":"<EMAIL>","level":"debug","message":"Using mock admin user","timestamp":"2025-05-29 15:07:05:75"}
{"email":"<EMAIL>","level":"debug","message":"Using mock admin user","timestamp":"2025-05-29 15:07:39:739"}
{"email":"<EMAIL>","level":"debug","message":"Using mock admin user","timestamp":"2025-05-29 15:07:39:739"}
{"email":"<EMAIL>","level":"debug","message":"Using mock admin user","timestamp":"2025-05-29 15:07:41:741"}
{"email":"<EMAIL>","level":"debug","message":"Using mock admin user","timestamp":"2025-05-29 15:07:43:743"}
{"email":"<EMAIL>","level":"debug","message":"Using mock admin user","timestamp":"2025-05-29 15:07:43:743"}
{"email":"<EMAIL>","level":"debug","message":"Using mock admin user","timestamp":"2025-05-29 15:07:45:745"}
{"email":"<EMAIL>","level":"debug","message":"Using mock admin user","timestamp":"2025-05-29 15:07:50:750"}
{"email":"<EMAIL>","level":"debug","message":"Using mock admin user","timestamp":"2025-05-29 15:07:50:750"}
{"email":"<EMAIL>","level":"debug","message":"Using mock admin user","timestamp":"2025-05-29 15:07:51:751"}
{"email":"<EMAIL>","level":"debug","message":"Using mock admin user","timestamp":"2025-05-29 15:07:53:753"}
{"email":"<EMAIL>","level":"debug","message":"Using mock admin user","timestamp":"2025-05-29 15:07:53:753"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:10:02:102"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:10:05:105"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:10:14:1014"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:10:47:1047"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:10:47:1047"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:10:49:1049"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:10:49:1049"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:11:08:118"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:11:23:1123"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:11:29:1129"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:14:27:1427"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:14:28:1428"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:14:28:1428"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:14:38:1438"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:14:38:1438"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:14:38:1438"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:14:52:1452"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:14:52:1452"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:14:52:1452"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:15:06:156"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:15:06:156"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:15:06:156"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:16:06:166"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:16:06:166"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:16:07:167"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:16:51:1651"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:16:51:1651"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:16:58:1658"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:16:58:1658"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:16:59:1659"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:16:59:1659"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:17:00:170"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:17:03:173"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:17:03:173"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:17:03:173"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:17:12:1712"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:17:18:1718"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:18:57:1857"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:19:03:193"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:19:03:193"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:19:06:196"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:19:06:196"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:19:06:196"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:19:11:1911"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:19:29:1929"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:19:32:1932"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:19:32:1932"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:19:33:1933"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:19:34:1934"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:19:35:1935"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:19:35:1935"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:19:36:1936"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:19:37:1937"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:19:37:1937"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:19:38:1938"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:19:39:1939"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:19:39:1939"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:19:42:1942"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:19:42:1942"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:19:42:1942"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:19:43:1943"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:19:44:1944"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:19:47:1947"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:19:48:1948"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:19:49:1949"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:19:49:1949"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:19:50:1950"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:19:53:1953"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:19:56:1956"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:19:59:1959"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:20:01:201"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:20:04:204"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:20:07:207"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:20:09:209"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:20:28:2028"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:20:30:2030"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:20:34:2034"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:24:12:2412"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:24:22:2422"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:24:34:2434"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:24:46:2446"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:24:58:2458"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:25:33:2533"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:25:38:2538"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:26:06:266"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:26:12:2612"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:26:27:2627"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:26:28:2628"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:26:34:2634"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:26:41:2641"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:26:41:2641"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:26:41:2641"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:26:44:2644"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:27:01:271"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:27:02:272"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:27:03:273"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:27:03:273"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:27:03:273"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:27:11:2711"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:27:13:2713"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:27:13:2713"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:27:13:2713"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 15:27:15:2715"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 16:31:19:3119"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 16:31:27:3127"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 16:31:27:3127"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 16:31:27:3127"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 16:31:28:3128"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 16:31:31:3131"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 16:31:32:3132"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 16:31:32:3132"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 16:31:35:3135"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 16:31:39:3139"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 16:31:40:3140"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-05-29 16:31:40:3140"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:33:18:3318"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:33:21:3321"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:33:22:3322"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:33:22:3322"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:33:37:3337"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:33:42:3342"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:33:45:3345"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:33:45:3345"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:33:45:3345"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:34:29:3429"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:34:52:3452"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:35:11:3511"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:35:11:3511"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:35:11:3511"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:35:11:3511"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:35:11:3511"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:35:11:3511"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:35:23:3523"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:35:23:3523"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:35:23:3523"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:35:58:3558"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:35:58:3558"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:35:58:3558"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:36:14:3614"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:36:14:3614"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:36:14:3614"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:36:28:3628"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:36:31:3631"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:36:32:3632"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:36:32:3632"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:36:32:3632"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:38:19:3819"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:38:22:3822"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:38:22:3822"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:38:22:3822"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:38:40:3840"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:38:41:3841"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:38:41:3841"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:38:41:3841"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:38:42:3842"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:38:44:3844"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:38:44:3844"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:38:44:3844"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:38:48:3848"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:38:49:3849"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:38:49:3849"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:38:49:3849"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:39:50:3950"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:39:55:3955"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:40:26:4026"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:40:26:4026"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:40:27:4027"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:40:27:4027"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:40:27:4027"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:40:27:4027"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:40:48:4048"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:41:39:4139"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:41:52:4152"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:41:56:4156"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:41:56:4156"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:41:56:4156"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:42:16:4216"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:42:30:4230"}
{"email":"<EMAIL>","level":"info","message":"Development mode: Admin login successful","timestamp":"2025-06-03 16:42:30:4230"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:43:16:4316"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:43:16:4316"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:43:16:4316"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:43:17:4317"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:43:17:4317"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:43:20:4320"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:43:20:4320"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:43:20:4320"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:43:20:4320"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:43:20:4320"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:43:20:4320"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:43:38:4338"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:43:38:4338"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:45:10:4510"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 16:46:27:4627"}
{"email":"<EMAIL>","level":"info","message":"Development mode: Admin login successful","timestamp":"2025-06-03 16:46:27:4627"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 16:46:27:4627"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 16:46:32:4632"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 16:46:32:4632"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 16:46:33:4633"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 16:46:37:4637"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 16:46:37:4637"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 16:48:50:4850"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 16:48:56:4856"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 16:48:57:4857"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 16:49:11:4911"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 16:49:13:4913"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 16:49:14:4914"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 16:49:36:4936"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 16:49:37:4937"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 16:49:38:4938"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 16:52:01:521"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 16:52:06:526"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 16:52:07:527"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 16:53:46:5346"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 16:55:37:5537"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 16:55:46:5546"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 16:55:46:5546"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 16:58:44:5844"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 16:58:44:5844"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 16:58:44:5844"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 16:58:49:5849"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 16:58:53:5853"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 16:58:53:5853"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 16:58:53:5853"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 16:59:07:597"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 16:59:23:5923"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 16:59:23:5923"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 16:59:23:5923"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:00:20:020"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:00:20:020"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:00:20:020"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:00:21:021"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:00:22:022"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:00:22:022"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:00:23:023"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:00:23:023"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:00:24:024"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:00:24:024"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:00:24:024"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:00:27:027"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:00:27:027"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:00:27:027"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:00:29:029"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:00:29:029"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:00:29:029"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:01:15:115"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:01:15:115"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:01:15:115"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:01:16:116"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:01:16:116"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:01:17:117"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:01:17:117"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:01:18:118"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:01:20:120"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:01:20:120"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:01:20:120"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:01:20:120"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:01:20:120"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:01:20:120"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:01:21:121"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:01:21:121"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:01:21:121"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:02:31:231"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:02:31:231"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:02:32:232"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:02:32:232"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:02:32:232"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:02:33:233"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:02:33:233"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:02:34:234"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:02:34:234"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:02:35:235"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:02:35:235"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:02:35:235"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:02:37:237"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:02:37:237"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:02:37:237"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:02:37:237"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:02:37:237"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:02:37:237"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:02:38:238"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:02:38:238"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:02:38:238"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:05:08:58"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:05:08:58"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:05:09:59"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:05:09:59"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:05:09:59"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:05:10:510"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:05:10:510"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:05:11:511"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:05:13:513"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:05:13:513"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:05:13:513"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:05:13:513"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:05:13:513"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:05:13:513"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:05:13:513"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:05:13:513"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:05:13:513"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:05:46:546"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:05:54:554"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:06:07:67"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:13:03:133"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:13:08:138"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:13:08:138"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:13:08:138"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:13:18:1318"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:13:19:1319"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:13:20:1320"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:17:26:1726"}
{"email":"<EMAIL>","level":"info","message":"Development mode: Admin login successful","timestamp":"2025-06-03 17:17:26:1726"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:17:26:1726"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:17:28:1728"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:17:29:1729"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:17:29:1729"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:17:30:1730"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:17:31:1731"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:17:32:1732"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:17:32:1732"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:17:33:1733"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:17:42:1742"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:17:45:1745"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:17:46:1746"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:17:52:1752"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:18:09:189"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:18:12:1812"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:18:15:1815"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:18:15:1815"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:18:15:1815"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:19:38:1938"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:26:46:2646"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:30:12:3012"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:30:17:3017"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:30:29:3029"}
{"email":"<EMAIL>","level":"info","message":"Development mode: Admin login successful","timestamp":"2025-06-03 17:30:29:3029"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:30:29:3029"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:30:32:3032"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:30:33:3033"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:30:34:3034"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:30:39:3039"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:31:55:3155"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:31:55:3155"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:31:57:3157"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:31:57:3157"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:32:01:321"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:32:01:321"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:32:01:321"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:32:05:325"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:32:07:327"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:32:07:327"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:32:07:327"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:32:08:328"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:32:08:328"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:32:09:329"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:32:10:3210"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:32:11:3211"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:32:12:3212"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:32:12:3212"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:32:18:3218"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:32:22:3222"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:32:26:3226"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:32:27:3227"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:32:32:3232"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:32:35:3235"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:32:38:3238"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:32:43:3243"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:32:43:3243"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:32:43:3243"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:33:06:336"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:33:49:3349"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:33:52:3352"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:33:54:3354"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:33:55:3355"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:33:55:3355"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:33:58:3358"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:33:58:3358"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:36:10:3610"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:36:46:3646"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:37:20:3720"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:37:24:3724"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:37:26:3726"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:37:27:3727"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:37:59:3759"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:37:59:3759"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:37:59:3759"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:38:47:3847"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:38:51:3851"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:38:54:3854"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:39:25:3925"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:39:29:3929"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:39:34:3934"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:39:34:3934"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:39:34:3934"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:39:40:3940"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:39:47:3947"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:39:49:3949"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:39:59:3959"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:40:05:405"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:40:10:4010"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:40:11:4011"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:40:32:4032"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:40:58:4058"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:41:04:414"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:44:13:4413"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:44:18:4418"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:44:22:4422"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:44:22:4422"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:44:22:4422"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:49:27:4927"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 17:49:40:4940"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 17:49:44:4944"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 17:49:44:4944"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 17:49:44:4944"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 17:49:52:4952"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 17:51:26:5126"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 17:51:26:5126"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 17:51:26:5126"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 17:51:39:5139"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 17:51:40:5140"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 17:51:44:5144"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 17:51:50:5150"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-03 17:51:53:5153"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:53:29:5329"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:53:31:5331"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:53:31:5331"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:53:31:5331"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:53:36:5336"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:53:41:5341"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:53:41:5341"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:53:41:5341"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:53:42:5342"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:53:46:5346"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:53:48:5348"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:53:50:5350"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:53:53:5353"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:54:42:5442"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:54:48:5448"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:54:53:5453"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:54:56:5456"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:55:00:550"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:55:00:550"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:55:00:550"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:55:12:5512"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:55:18:5518"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:55:38:5538"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:55:41:5541"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:55:47:5547"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:55:48:5548"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:55:48:5548"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:55:48:5548"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:55:49:5549"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:55:49:5549"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:55:50:5550"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:55:50:5550"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:55:51:5551"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:55:59:5559"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:56:01:561"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:56:01:561"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:56:01:561"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:56:08:568"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:56:08:568"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:56:09:569"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 17:56:23:5623"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 18:01:07:17"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 18:01:13:113"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 18:01:24:124"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 18:01:24:124"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 18:01:24:124"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 18:01:27:127"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 18:01:31:131"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 18:01:31:131"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 18:01:31:131"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 18:02:14:214"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-03 18:02:16:216"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-04 09:58:49:5849"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-05 21:00:57:057"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-05 21:01:02:12"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-05 21:01:03:13"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-05 21:01:03:13"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-05 21:03:02:32"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-05 21:03:05:35"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-05 21:03:05:35"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-05 21:03:05:35"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-05 21:03:17:317"}
{"email":"<EMAIL>","level":"info","message":"Development mode: Admin login successful","timestamp":"2025-06-05 21:03:17:317"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:03:23:323"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:03:37:337"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:03:37:337"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:03:46:346"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:05:17:517"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:05:17:517"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:05:18:518"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:05:22:522"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:05:25:525"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:05:26:526"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:05:31:531"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:05:35:535"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:05:36:536"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:05:40:540"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:05:44:544"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:05:51:551"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:05:52:552"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:06:11:611"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:06:11:611"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:06:14:614"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:06:19:619"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:06:21:621"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:06:21:621"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:06:24:624"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:06:25:625"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:06:25:625"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:06:28:628"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:06:38:638"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:08:50:850"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:08:50:850"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:08:50:850"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:08:50:850"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:08:54:854"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:09:34:934"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:09:34:934"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:09:35:935"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:09:35:935"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:09:40:940"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:11:25:1125"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:11:29:1129"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:11:29:1129"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:11:29:1129"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:11:44:1144"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:11:56:1156"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:12:14:1214"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:12:31:1231"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:12:49:1249"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:12:51:1251"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:12:55:1255"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:13:03:133"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:13:15:1315"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:17:26:1726"}
{"email":"<EMAIL>","level":"info","message":"Development mode: Admin login successful","timestamp":"2025-06-05 21:17:26:1726"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:17:26:1726"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:17:28:1728"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:18:13:1813"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:18:19:1819"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:18:22:1822"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:18:25:1825"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:18:26:1826"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:18:29:1829"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:18:59:1859"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:20:03:203"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:20:04:204"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:20:04:204"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:20:04:204"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:20:09:209"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:21:14:2114"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:21:15:2115"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:23:05:235"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:23:05:235"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:23:33:2333"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:23:33:2333"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:23:33:2333"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:24:23:2423"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:24:24:2424"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:24:28:2428"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:24:31:2431"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:25:23:2523"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:25:26:2526"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:25:26:2526"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:25:26:2526"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:25:44:2544"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:25:58:2558"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:26:16:2616"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:26:20:2620"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:26:43:2643"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:26:49:2649"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:26:49:2649"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:26:51:2651"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:26:54:2654"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:26:56:2656"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:27:02:272"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:27:04:274"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:27:05:275"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:27:06:276"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:27:07:277"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:27:40:2740"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:27:41:2741"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:27:41:2741"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:28:14:2814"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:28:18:2818"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:31:46:3146"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:32:34:3234"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:36:18:3618"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:36:25:3625"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:36:27:3627"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:36:29:3629"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:38:00:380"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:39:05:395"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:39:06:396"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:39:06:396"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:39:06:396"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:39:10:3910"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:39:12:3912"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:39:15:3915"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:39:22:3922"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:39:22:3922"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:39:22:3922"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:39:41:3941"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:39:42:3942"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:39:43:3943"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:39:43:3943"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:39:47:3947"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:39:48:3948"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:39:49:3949"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:39:50:3950"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:39:50:3950"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:39:51:3951"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:39:51:3951"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:39:58:3958"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:39:59:3959"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:39:59:3959"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:40:01:401"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:40:03:403"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:40:07:407"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:40:08:408"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:40:08:408"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:40:09:409"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:40:10:4010"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:40:11:4011"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:40:12:4012"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:40:14:4014"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:40:20:4020"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:42:00:420"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:42:01:421"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:42:01:421"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:42:01:421"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:44:32:4432"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:44:32:4432"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:44:32:4432"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:44:33:4433"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:44:34:4434"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:44:34:4434"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:44:35:4435"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:44:35:4435"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:44:35:4435"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:44:36:4436"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:44:37:4437"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:44:37:4437"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:44:37:4437"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:44:37:4437"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:44:37:4437"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:44:39:4439"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:44:39:4439"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:44:40:4440"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:44:40:4440"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:44:40:4440"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:44:40:4440"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:47:33:4733"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:47:39:4739"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:47:39:4739"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:47:40:4740"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:47:40:4740"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:47:41:4741"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:47:41:4741"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:47:42:4742"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:47:43:4743"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:47:48:4748"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:47:48:4748"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:47:48:4748"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:47:48:4748"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:47:50:4750"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:47:50:4750"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:47:50:4750"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:47:50:4750"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:47:50:4750"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:47:50:4750"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:47:56:4756"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:47:56:4756"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:47:56:4756"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:47:57:4757"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:47:57:4757"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:47:58:4758"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:47:58:4758"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:47:59:4759"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:47:59:4759"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:48:00:480"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:48:00:480"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:48:00:480"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:48:00:480"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:48:00:480"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:48:00:480"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:48:00:480"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:48:02:482"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:48:02:482"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:48:02:482"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:50:31:5031"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:50:39:5039"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:50:46:5046"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:50:47:5047"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:51:10:5110"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:51:53:5153"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:52:57:5257"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:52:57:5257"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:52:58:5258"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:53:01:531"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:53:09:539"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:53:13:5313"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:54:58:5458"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:55:00:550"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:55:01:551"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:55:03:553"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:55:08:558"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:55:44:5544"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:55:45:5545"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:55:47:5547"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:55:47:5547"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 21:55:47:5547"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:00:04:04"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:00:04:04"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:00:04:04"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:00:10:010"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:00:11:011"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:00:11:011"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:00:14:014"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:00:23:023"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:00:54:054"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:00:57:057"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:01:05:15"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:01:05:15"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:01:06:16"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:01:16:116"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:01:17:117"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:01:17:117"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:01:18:118"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:01:22:122"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:01:22:122"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:01:22:122"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:01:26:126"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:01:26:126"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:01:27:127"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:01:33:133"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:01:33:133"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:01:33:133"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:01:36:136"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:01:38:138"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:02:31:231"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:02:33:233"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:02:34:234"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:02:34:234"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:02:35:235"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:02:36:236"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:02:36:236"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:02:36:236"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:02:37:237"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:02:54:254"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:02:55:255"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:02:55:255"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:02:57:257"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:03:03:33"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:03:03:33"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:03:04:34"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:03:06:36"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:03:06:36"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:03:08:38"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:03:11:311"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:03:11:311"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:03:12:312"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:03:12:312"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:03:13:313"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:03:15:315"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:03:15:315"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:03:16:316"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:03:18:318"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:03:18:318"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:03:18:318"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:03:19:319"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:03:19:319"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:03:19:319"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:03:19:319"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:05:01:51"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:07:07:77"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:07:07:77"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:07:08:78"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:07:09:79"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:07:11:711"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:07:11:711"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:07:12:712"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:07:15:715"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:07:16:716"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:07:16:716"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:07:16:716"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:07:16:716"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:07:16:716"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:07:18:718"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:07:18:718"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:07:18:718"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:08:13:813"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:08:15:815"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:08:15:815"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:08:15:815"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:08:42:842"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:08:42:842"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:08:42:842"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:08:43:843"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:08:54:854"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:09:30:930"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:09:32:932"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:09:34:934"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:09:34:934"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:09:34:934"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:09:57:957"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:09:57:957"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:09:57:957"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:09:57:957"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:09:57:957"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:09:57:957"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:09:57:957"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:09:57:957"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:09:57:957"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:09:57:957"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:09:57:957"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:09:57:957"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:09:57:957"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:09:57:957"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:10:13:1013"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:10:13:1013"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:10:13:1013"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:10:13:1013"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:10:13:1013"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:10:13:1013"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:10:13:1013"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:10:13:1013"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:10:13:1013"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:10:13:1013"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:10:13:1013"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:10:13:1013"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:10:13:1013"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:10:13:1013"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:10:34:1034"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:10:35:1035"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:10:35:1035"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:10:35:1035"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:10:36:1036"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:10:36:1036"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:10:36:1036"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:10:36:1036"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:10:36:1036"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:10:36:1036"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:10:36:1036"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:10:36:1036"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:10:36:1036"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:10:36:1036"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:10:37:1037"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:10:37:1037"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:10:37:1037"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:10:37:1037"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:10:37:1037"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:10:37:1037"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:10:37:1037"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:10:37:1037"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:10:37:1037"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:10:37:1037"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:10:37:1037"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:10:39:1039"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:10:40:1040"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:11:01:111"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:11:02:112"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:11:02:112"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:11:02:112"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:11:02:112"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:11:02:112"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:11:03:113"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:11:03:113"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:11:03:113"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:11:03:113"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:11:04:114"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:11:04:114"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:11:04:114"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:11:04:114"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:11:04:114"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:11:04:114"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:11:04:114"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:11:04:114"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:11:05:115"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:11:05:115"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:11:05:115"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:11:05:115"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:11:05:115"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:11:05:115"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:11:06:116"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:11:06:116"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:11:06:116"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:15:26:1526"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:15:26:1526"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:15:26:1526"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:15:26:1526"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:15:26:1526"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:15:26:1526"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:15:26:1526"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:15:26:1526"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:15:26:1526"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:15:26:1526"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:15:26:1526"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:15:26:1526"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:15:26:1526"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:15:26:1526"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:16:20:1620"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:16:22:1622"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:16:22:1622"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:16:22:1622"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:16:35:1635"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:16:43:1643"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:16:44:1644"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:16:45:1645"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:16:49:1649"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:16:51:1651"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:17:01:171"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:26:46:2646"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:26:47:2647"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:26:48:2648"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:26:48:2648"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:26:48:2648"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 22:27:11:2711"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 23:05:38:538"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 23:05:42:542"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 23:05:43:543"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 23:05:45:545"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 23:05:50:550"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 23:05:47:547"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 23:05:48:548"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 23:05:49:549"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 23:13:45:1345"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 23:13:48:1348"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 23:13:48:1348"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 23:13:48:1348"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 23:17:13:1713"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 23:17:15:1715"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 23:17:17:1717"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 23:37:23:3723"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-05 23:56:50:5650"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-06 00:44:19:4419"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-06 07:38:36:3836"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-06 08:45:35:4535"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-06 08:45:35:4535"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-06 08:45:35:4535"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-06 08:51:45:5145"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-06 08:51:45:5145"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-06 08:51:48:5148"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-06 08:51:48:5148"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-06 08:51:48:5148"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-06 08:51:51:5151"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:15:07:157"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:15:15:1515"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:15:15:1515"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:15:15:1515"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:15:40:1540"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:21:30:2130"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:21:30:2130"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:21:30:2130"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:21:47:2147"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:22:18:2218"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:22:19:2219"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:22:23:2223"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:22:23:2223"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:22:23:2223"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:22:27:2227"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:22:28:2228"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:22:33:2233"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:22:33:2233"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:22:33:2233"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:27:13:2713"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:27:18:2718"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:27:20:2720"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:27:20:2720"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:27:20:2720"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:27:51:2751"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:27:51:2751"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:27:55:2755"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:27:55:2755"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:27:55:2755"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:28:57:2857"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:28:57:2857"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:29:00:290"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:29:00:290"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:29:00:290"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:29:29:2929"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:29:39:2939"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:30:53:3053"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:30:53:3053"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:30:54:3054"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:30:54:3054"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:30:57:3057"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:30:57:3057"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:30:57:3057"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:30:57:3057"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:31:33:3133"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:33:52:3352"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:33:52:3352"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:33:52:3352"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:33:52:3352"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:33:53:3353"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:35:20:3520"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:35:21:3521"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:38:38:3838"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:38:43:3843"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:39:34:3934"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:40:03:403"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:40:38:4038"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:43:11:4311"}
{"email":"<EMAIL>","level":"info","message":"New user registered","timestamp":"2025-06-12 14:43:11:4311","userId":2,"username":"LisaDunn95"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:44:03:443"}
{"email":"<EMAIL>","level":"info","message":"Development mode: Admin login successful","timestamp":"2025-06-12 14:44:03:443"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 14:44:04:444"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 14:44:10:4410"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:47:18:4718"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:48:06:486"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:48:07:487"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:48:07:487"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:48:07:487"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:48:42:4842"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 14:48:42:4842"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 14:48:42:4842"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 14:48:43:4843"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 14:48:43:4843"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 14:48:43:4843"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:48:45:4845"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:48:45:4845"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:48:45:4845"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:49:22:4922"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 14:49:23:4923"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 14:49:23:4923"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 14:49:23:4923"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:49:26:4926"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:49:26:4926"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:49:26:4926"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 14:49:28:4928"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-12 14:49:28:4928","total":2}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:50:24:5024"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 14:50:25:5025"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 14:50:25:5025"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 14:50:26:5026"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:50:29:5029"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:50:29:5029"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:50:29:5029"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 14:50:30:5030"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-12 14:50:30:5030","total":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 14:50:38:5038"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-12 14:50:38:5038","total":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 14:50:52:5052"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-12 14:50:52:5052","total":2}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:50:57:5057"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 14:50:58:5058"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 14:50:58:5058"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 14:50:58:5058"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 14:50:59:5059"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 14:50:59:5059"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:51:02:512"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:51:02:512"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:51:02:512"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 14:51:04:514"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-12 14:51:04:514","total":2}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-12 14:51:07:517"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 14:51:16:5116"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-12 14:51:16:5116","total":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 14:51:16:5116"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-12 14:51:16:5116","total":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 14:53:11:5311"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 14:53:19:5319"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-12 14:53:19:5319","total":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 14:53:51:5351"}
{"adminUser":"admin","level":"info","message":"Admin accessed user details","targetUser":"LisaDunn95","timestamp":"2025-06-12 14:53:51:5351","userId":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 14:55:10:5510"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-12 14:55:10:5510","total":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 14:55:23:5523"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 14:55:27:5527"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-12 14:55:27:5527","total":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 14:55:28:5528"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-12 14:55:28:5528","total":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 14:56:02:562"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 14:56:07:567"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-12 14:56:07:567","total":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 14:57:42:5742"}
{"adminUser":"admin","level":"info","message":"Admin accessed user details","targetUser":"LisaDunn95","timestamp":"2025-06-12 14:57:42:5742","userId":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 14:57:50:5750"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 14:57:56:5756"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-12 14:57:56:5756","total":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 14:59:49:5949"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 14:59:49:5949"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 14:59:52:5952"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 14:59:52:5952"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 14:59:53:5953"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 14:59:54:5954"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-12 14:59:54:5954","total":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 14:59:58:5958"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 14:59:59:5959"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 15:00:06:06"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-12 15:00:06:06","total":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 15:00:51:051"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 15:00:57:057"}
{"adminUser":"admin","level":"info","message":"Admin accessed user details","targetUser":"LisaDunn95","timestamp":"2025-06-12 15:00:57:057","userId":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 15:01:13:113"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 15:01:33:133"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 15:01:34:134"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-12 15:01:34:134","total":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 15:03:09:39"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 15:03:14:314"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-12 15:03:14:314","total":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 15:03:46:346"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 15:03:47:347"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 15:04:26:426"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 15:04:31:431"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-12 15:04:31:431","total":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 15:04:46:446"}
{"adminUser":"admin","level":"info","message":"Admin accessed user details","targetUser":"LisaDunn95","timestamp":"2025-06-12 15:04:46:446","userId":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 15:06:37:637"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 15:06:37:637"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-12 15:06:37:637","total":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 15:06:37:637"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-12 15:06:37:637","total":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 15:06:37:637"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-12 15:06:37:637","total":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 15:07:00:70"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 15:07:10:710"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-12 15:07:10:710","total":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 15:07:11:711"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-12 15:07:11:711","total":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 15:07:11:711"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-12 15:07:11:711","total":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 15:07:44:744"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 15:07:55:755"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-12 15:07:55:755","total":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 15:07:55:755"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-12 15:07:55:755","total":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 15:07:55:755"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-12 15:07:55:755","total":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 15:08:43:843"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 15:08:47:847"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 15:08:57:857"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-12 15:08:57:857","total":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 15:08:58:858"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-12 15:08:58:858","total":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 15:08:58:858"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-12 15:08:58:858","total":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 15:08:58:858"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-12 15:08:58:858","total":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 15:08:58:858"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-12 15:08:58:858","total":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 15:08:58:858"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-12 15:08:58:858","total":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 15:09:29:929"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 15:09:30:930"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-12 15:09:30:930","total":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 15:09:30:930"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-12 15:09:30:930","total":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 15:09:30:930"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-12 15:09:30:930","total":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 15:09:49:949"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 15:09:53:953"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-12 15:09:53:953","total":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 15:09:53:953"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-12 15:09:53:953","total":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 15:09:53:953"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-12 15:09:53:953","total":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 15:10:29:1029"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 15:10:55:1055"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 15:11:05:115"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-12 15:11:05:115","total":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 15:11:05:115"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-12 15:11:05:115","total":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 15:11:05:115"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-12 15:11:05:115","total":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 15:11:44:1144"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 15:11:47:1147"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 15:11:58:1158"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-12 15:11:58:1158","total":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 15:11:58:1158"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-12 15:11:58:1158","total":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 15:11:59:1159"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-12 15:11:59:1159","total":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 15:11:59:1159"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-12 15:11:59:1159","total":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 15:11:59:1159"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-12 15:11:59:1159","total":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 15:11:59:1159"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-12 15:11:59:1159","total":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 15:12:16:1216"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 15:12:37:1237"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 15:12:49:1249"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-12 15:12:49:1249","total":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 15:18:53:1853"}
{"adminUser":"admin","level":"info","message":"Admin accessed user details","targetUser":"LisaDunn95","timestamp":"2025-06-12 15:18:53:1853","userId":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 15:18:58:1858"}
{"adminUser":"admin","deletedUser":"LisaDunn95","level":"info","message":"User deleted by admin","timestamp":"2025-06-12 15:18:58:1858","userId":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-12 15:18:58:1858"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-12 15:18:58:1858","total":1}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-26 14:14:59:1459"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-26 14:15:01:151"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-26 14:15:01:151"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-26 14:15:01:151"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-26 14:15:18:1518"}
{"email":"<EMAIL>","level":"info","message":"Development mode: Admin login successful","timestamp":"2025-06-26 14:15:18:1518"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 14:15:18:1518"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 14:15:23:1523"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 14:15:23:1523"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 14:15:23:1523"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 14:15:26:1526"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 14:15:26:1526"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 14:15:26:1526","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 14:30:50:3050"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 14:30:50:3050","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 14:31:01:311"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 14:31:01:311","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 14:31:13:3113"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 14:31:13:3113"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 14:31:13:3113","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 14:31:16:3116"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 14:31:16:3116","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 14:31:23:3123"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 14:31:23:3123","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 14:31:35:3135"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 14:31:35:3135","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 14:31:55:3155"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 14:31:55:3155","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 14:32:09:329"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 14:32:09:329","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 14:32:22:3222"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 14:32:22:3222","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 14:32:36:3236"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 14:32:36:3236","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 14:32:50:3250"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 14:32:50:3250","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 14:33:15:3315"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 14:33:15:3315","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 14:34:48:3448"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 14:34:48:3448","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 14:35:03:353"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 14:35:03:353","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 14:41:52:4152"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 14:41:55:4155"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 14:41:55:4155","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 14:43:36:4336"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 14:43:37:4337"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 14:43:37:4337","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 14:44:52:4452"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 14:44:53:4453"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 14:44:53:4453","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 14:45:46:4546"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 14:45:47:4547"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 14:45:47:4547","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 14:46:00:460"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 14:46:00:460","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 14:46:06:466"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 14:46:07:467"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 14:46:07:467","total":1}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-26 14:46:09:469"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-26 14:46:09:469"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-26 14:47:11:4711"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 14:47:11:4711"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 14:47:13:4713"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 14:47:13:4713","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 14:49:44:4944"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-26 14:49:45:4945"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 14:49:45:4945"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 14:49:45:4945","total":1}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-26 14:49:52:4952"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 14:49:53:4953"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 14:49:53:4953","total":1}
{"level":"info","message":"Starting content scheduler","timestamp":"2025-06-26 14:51:11:5111"}
{"level":"info","message":"Content scheduler initialized","timestamp":"2025-06-26 14:51:11:5111"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-26 14:51:11:5111"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-26 14:51:11:5111"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-26 14:51:17:5117"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 14:51:17:5117"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-26 14:51:17:5117"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 14:51:19:5119"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 14:51:19:5119","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 14:52:22:5222"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-26 14:52:23:5223"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-26 14:52:23:5223"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 14:52:23:5223"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 14:52:23:5223","total":1}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-26 14:55:20:5520"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 14:55:20:5520"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-26 14:55:20:5520"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-26 14:55:21:5521"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 14:55:21:5521"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 14:55:21:5521","total":1}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-26 14:56:17:5617"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 14:56:17:5617"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-26 14:56:17:5617"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 14:56:18:5618"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 14:56:18:5618","total":1}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-26 14:56:27:5627"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-26 14:56:27:5627"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 14:56:27:5627"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 14:56:27:5627","total":1}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-26 14:56:37:5637"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-26 14:56:37:5637"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-26 14:56:58:5658"}
{"email":"<EMAIL>","level":"info","message":"Development mode: Admin login successful","timestamp":"2025-06-26 14:56:58:5658"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 14:56:58:5658"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 14:57:53:5753"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 14:57:53:5753"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 14:57:53:5753"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 14:57:54:5754"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 14:57:55:5755"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 14:57:55:5755","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 14:58:00:580"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 14:58:01:581"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 14:58:01:581"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 14:58:02:582"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 14:58:02:582","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 14:59:11:5911"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 14:59:11:5911"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 14:59:11:5911"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 14:59:12:5912"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 14:59:12:5912","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:00:28:028"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:00:28:028"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:00:28:028"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:00:29:029"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 15:00:29:029","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:00:38:038"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:00:38:038"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 15:00:38:038","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:00:47:047"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:00:48:048"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:00:48:048"}
{"adminUser":"admin","level":"info","limit":100,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 15:00:48:048","total":0}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:00:48:048"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:06:07:67"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:06:07:67"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:06:07:67"}
{"adminUser":"admin","level":"info","limit":100,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 15:06:07:67","total":0}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:06:07:67"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:06:14:614"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:06:15:615"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:06:19:619"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:06:20:620"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:06:20:620"}
{"adminUser":"admin","level":"info","limit":100,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 15:06:20:620","total":0}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:06:20:620"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:08:44:844"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:08:46:846"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:08:47:847"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:08:48:848"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:08:48:848"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:08:49:849"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:08:50:850"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:08:51:851"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:08:52:852"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 15:08:52:852","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:09:09:99"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:09:10:910"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:09:10:910"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:10:18:1018"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:10:28:1028"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:10:40:1040"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:11:07:117"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:11:07:117"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 15:11:07:117","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:11:07:117"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 15:11:07:117","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:11:20:1120"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:11:21:1121"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 15:11:21:1121","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:11:21:1121"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 15:11:21:1121","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:11:31:1131"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:11:31:1131"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 15:11:31:1131","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:11:31:1131"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 15:11:31:1131","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:11:55:1155"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:12:57:1257"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:12:57:1257"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:12:57:1257"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:12:58:1258"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:13:05:135"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 15:13:05:135","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:13:05:135"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:13:05:135"}
{"adminUser":"admin","level":"info","limit":100,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 15:13:05:135","total":0}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:13:05:135"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:13:06:136"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 15:13:06:136","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:13:37:1337"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:13:38:1338"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:13:39:1339"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:13:40:1340"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:13:42:1342"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 15:13:42:1342","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:13:42:1342"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:13:42:1342"}
{"adminUser":"admin","level":"info","limit":100,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 15:13:42:1342","total":0}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:13:42:1342"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:13:43:1343"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 15:13:43:1343","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:13:45:1345"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:13:46:1346"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:13:47:1347"}
{"adminUser":"admin","level":"info","limit":100,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 15:13:47:1347","total":0}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:13:47:1347"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:15:49:1549"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:15:49:1549"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:15:52:1552"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:15:52:1552"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:15:53:1553"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:15:54:1554"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 15:15:54:1554","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:15:54:1554"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:15:54:1554"}
{"adminUser":"admin","level":"info","limit":100,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 15:15:54:1554","total":0}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:15:54:1554"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:15:54:1554"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 15:15:54:1554","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:15:54:1554"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:15:54:1554"}
{"adminUser":"admin","level":"info","limit":100,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 15:15:54:1554","total":0}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:15:54:1554"}
{"level":"info","message":"Starting content scheduler","timestamp":"2025-06-26 15:16:14:1614"}
{"level":"info","message":"Content scheduler initialized","timestamp":"2025-06-26 15:16:14:1614"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:16:14:1614"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:17:00:170"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:17:01:171"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:17:17:1717"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:17:17:1717"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 15:17:17:1717","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:23:26:2326"}
{"adminUser":"admin","createdUser":"FinnWolfhard_Actual","level":"info","message":"User created by admin","role":"user","timestamp":"2025-06-26 15:23:26:2326"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:23:26:2326"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 15:23:26:2326","total":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:29:51:2951"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:29:52:2952"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:29:52:2952"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:29:55:2955"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:29:55:2955"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:29:56:2956"}
{"adminUser":"admin","level":"info","message":"Admin accessed user details","targetUser":"FinnWolfhard_Actual","timestamp":"2025-06-26 15:29:55:2955","userId":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:29:57:2957"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 15:29:57:2957","total":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:29:57:2957"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:29:58:2958"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:30:01:301"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:30:01:301"}
{"adminUser":"admin","level":"info","limit":100,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 15:30:01:301","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:30:01:301"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:30:01:301"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 15:30:01:301","total":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:30:01:301"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:30:01:301"}
{"adminUser":"admin","level":"info","limit":100,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 15:30:01:301","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:30:01:301"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 15:30:02:302"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 15:30:02:302","total":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 16:05:14:514"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 16:05:14:514"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 16:05:15:515"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 16:05:16:516"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 16:05:17:517"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 16:05:18:518"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 16:05:19:519"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 16:05:19:519","total":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 16:05:20:520"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 16:05:20:520"}
{"adminUser":"admin","level":"info","limit":100,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 16:05:20:520","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 16:05:20:520"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 16:05:20:520","total":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 16:05:20:520"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 16:05:20:520"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 16:05:20:520","total":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 16:05:20:520"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 16:05:20:520"}
{"adminUser":"admin","level":"info","limit":100,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 16:05:20:520","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 16:05:20:520"}
{"level":"info","message":"Starting content scheduler","timestamp":"2025-06-26 16:32:52:3252"}
{"level":"info","message":"Content scheduler initialized","timestamp":"2025-06-26 16:32:52:3252"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-26 16:32:52:3252"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-26 16:32:56:3256"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-26 16:32:56:3256"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-26 16:32:56:3256"}
{"level":"debug","message":"No session cookie found","timestamp":"2025-06-26 16:33:24:3324"}
{"email":"<EMAIL>","level":"info","message":"Development mode: Admin login successful","timestamp":"2025-06-26 16:33:24:3324"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 16:33:25:3325"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 16:34:03:343"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 16:34:04:344"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 16:34:04:344"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 16:34:07:347"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 16:34:07:347"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 16:34:07:347"}
{"adminUser":"admin","level":"info","limit":100,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 16:34:07:347","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 16:34:07:347"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 16:34:18:3418"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 16:34:22:3422"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 16:34:22:3422"}
{"adminUser":"admin","level":"info","limit":100,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 16:34:22:3422","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 16:34:23:3423"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 16:38:28:3828"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 16:38:29:3829"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 16:38:29:3829"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 16:38:30:3830"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 16:38:31:3831"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 16:38:31:3831"}
{"adminUser":"admin","level":"info","limit":100,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 16:38:31:3831","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 16:38:31:3831"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 16:47:45:4745"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 16:47:55:4755"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 16:47:56:4756"}
{"adminUser":"admin","level":"info","limit":100,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 16:47:56:4756","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 16:47:57:4757"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 16:53:13:5313"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 16:53:13:5313"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 16:53:15:5315"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 16:53:15:5315"}
{"adminUser":"admin","level":"info","limit":100,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 16:53:15:5315","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 16:53:15:5315"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 16:54:46:5446"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 16:54:46:5446"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 16:54:46:5446"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 16:54:50:5450"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 16:54:50:5450"}
{"adminUser":"admin","level":"info","limit":100,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 16:54:50:5450","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 16:54:50:5450"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 16:55:01:551"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 16:55:06:556"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 16:55:06:556","total":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 16:56:24:5624"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 16:56:24:5624"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 16:56:27:5627"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 16:56:27:5627","total":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 16:56:27:5627"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 16:56:27:5627"}
{"adminUser":"admin","level":"info","limit":100,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 16:56:27:5627","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 16:56:27:5627"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 16:56:54:5654"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 16:56:54:5654"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 16:56:54:5654"}
{"adminUser":"admin","level":"info","limit":100,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 16:56:54:5654","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 16:56:54:5654"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 16:57:10:5710"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 16:58:17:5817"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 16:58:17:5817"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 16:58:20:5820"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 16:58:20:5820","total":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 16:58:21:5821"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 16:58:21:5821"}
{"adminUser":"admin","level":"info","limit":100,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 16:58:21:5821","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 16:58:21:5821"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:00:41:041"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:00:42:042"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:00:43:043"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 17:00:43:043","total":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:00:45:045"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:00:45:045"}
{"adminUser":"admin","level":"info","limit":100,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 17:00:45:045","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:00:45:045"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:03:40:340"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:03:42:342"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:03:42:342"}
{"adminUser":"admin","level":"info","limit":100,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 17:03:42:342","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:03:42:342"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:03:46:346"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:03:59:359"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:03:59:359"}
{"adminUser":"admin","level":"info","limit":100,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 17:04:00:40","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:04:00:40"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:05:06:56"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:05:06:56"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:05:08:58"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:05:09:59"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:05:10:510"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:05:10:510"}
{"adminUser":"admin","level":"info","limit":100,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 17:05:10:510","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:05:10:510"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:05:13:513"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 17:05:13:513","total":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:05:14:514"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:05:14:514"}
{"adminUser":"admin","level":"info","limit":100,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 17:05:14:514","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:05:14:514"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:05:51:551"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:05:51:551"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:05:51:551"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:05:54:554"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:05:55:555"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:05:56:556"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:05:56:556"}
{"adminUser":"admin","level":"info","limit":100,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 17:05:56:556","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:05:56:556"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:05:57:557"}
{"adminUser":"admin","level":"info","limit":20,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 17:05:57:557","total":2}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:06:01:61"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:06:01:61"}
{"adminUser":"admin","level":"info","limit":100,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 17:06:01:61","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:06:01:61"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:06:26:626"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:06:27:627"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:06:27:627"}
{"adminUser":"admin","level":"info","limit":100,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 17:06:27:627","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:06:27:627"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:09:24:924"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:09:26:926"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:09:26:926"}
{"adminUser":"admin","level":"info","limit":100,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 17:09:26:926","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:09:26:926"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:09:53:953"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:09:56:956"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:09:57:957"}
{"adminUser":"admin","level":"info","limit":100,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 17:09:57:957","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:09:57:957"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:11:01:111"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:11:06:116"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:11:06:116"}
{"adminUser":"admin","level":"info","limit":100,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 17:11:06:116","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:11:06:116"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:11:53:1153"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:11:53:1153"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:11:55:1155"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:11:55:1155"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:11:57:1157"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:11:57:1157"}
{"adminUser":"admin","level":"info","limit":100,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 17:11:57:1157","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:11:57:1157"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:11:57:1157"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:11:57:1157"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:11:57:1157"}
{"adminUser":"admin","level":"info","limit":100,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 17:11:57:1157","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:11:57:1157"}
{"adminUser":"admin","level":"info","limit":100,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 17:11:57:1157","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:11:57:1157"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:11:57:1157"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:11:57:1157"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:11:57:1157"}
{"adminUser":"admin","level":"info","limit":100,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 17:11:57:1157","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:11:57:1157"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:12:29:1229"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:12:31:1231"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:12:31:1231"}
{"adminUser":"admin","level":"info","limit":100,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 17:12:31:1231","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:12:31:1231"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:19:19:1919"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:19:24:1924"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:19:24:1924"}
{"adminUser":"admin","level":"info","limit":100,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 17:19:24:1924","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:19:24:1924"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:20:54:2054"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:20:59:2059"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:20:59:2059"}
{"adminUser":"admin","level":"info","limit":100,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 17:20:59:2059","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:20:59:2059"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:21:54:2154"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:21:55:2155"}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:21:56:2156"}
{"adminUser":"admin","level":"info","limit":100,"message":"Admin users list accessed","page":1,"role":"","search":"","timestamp":"2025-06-26 17:21:56:2156","total":1}
{"email":"<EMAIL>","level":"debug","message":"User authenticated from database","role":"admin","timestamp":"2025-06-26 17:21:56:2156"}
